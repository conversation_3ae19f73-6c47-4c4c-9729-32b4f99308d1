# Syriana Software - الموقع الرسمي

موقع شركة Syriana Software الرسمي - شريكك الرقمي المتكامل للنمو

## 🌟 نظرة عامة

موقع إلكتروني احترافي لشركة Syriana Software، مطور باستخدام أحدث التقنيات لتقديم تجربة مستخدم استثنائية. الموقع يعرض خدمات الشركة وبرامجها المختلفة مع تصميم عصري ومتجاوب.

## 🚀 التقنيات المستخدمة

- **Next.js 15.4.2** - إطار عمل React للتطبيقات الحديثة
- **React 19.1.0** - مكتبة JavaScript لبناء واجهات المستخدم
- **TypeScript 5.8.3** - لغة برمجة مطورة من JavaScript
- **Tailwind CSS 3.4.17** - إطار عمل CSS للتصميم السريع
- **Framer Motion 12.23.6** - مكتبة الحركات والانتقالات
- **Heroicons** - مجموعة أيقونات احترافية

## 📁 هيكل المشروع

```
src/
├── app/                    # صفحات التطبيق (App Router)
│   ├── about/             # صفحة من نحن
│   ├── contact/           # صفحة التواصل
│   ├── programs/          # صفحات البرامج
│   │   └── dash/         # صفحة برنامج dash
│   ├── services/          # صفحات الخدمات
│   │   ├── web-development/      # تطوير المواقع
│   │   └── mobile-development/   # تطوير التطبيقات
│   ├── globals.css        # ملف الأنماط العامة
│   ├── layout.tsx         # التخطيط الأساسي
│   └── page.tsx          # الصفحة الرئيسية
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── layout/           # مكونات التخطيط
│   │   ├── Header.tsx    # رأس الصفحة
│   │   └── Footer.tsx    # تذييل الصفحة
│   └── sections/         # أقسام الصفحة الرئيسية
│       ├── HeroSection.tsx
│       ├── WhoWeHelpSection.tsx
│       ├── OurSolutionsSection.tsx
│       ├── JourneySection.tsx
│       └── CTASection.tsx
└── lib/                  # المكتبات والأدوات المساعدة
```

## 🎨 المميزات

### التصميم والواجهة
- ✅ تصميم متجاوب بالكامل (Responsive Design)
- ✅ دعم اللغة العربية مع RTL
- ✅ نظام ألوان متسق ومتطور
- ✅ خط Tajawal الاحترافي
- ✅ حركات وانتقالات سلسة مع Framer Motion

### الوظائف
- ✅ Mega Menu تفاعلي للتنقل
- ✅ نموذج تواصل متكامل
- ✅ صفحات مخصصة لكل خدمة وبرنامج
- ✅ قسم الأسئلة الشائعة
- ✅ معلومات الاتصال الشاملة

### الأداء والتحسين
- ✅ تحسين محركات البحث (SEO)
- ✅ Meta Tags شاملة
- ✅ Open Graph للمشاركة الاجتماعية
- ✅ تحميل سريع ومحسن
- ✅ صور محسنة للويب

## 🛠️ التثبيت والتشغيل

### المتطلبات
- Node.js 18.0 أو أحدث
- npm أو yarn أو pnpm

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd site-syriana
```

2. **تثبيت التبعيات**
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **تشغيل المشروع في وضع التطوير**
```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

4. **فتح المتصفح**
افتح [http://localhost:3000](http://localhost:3000) لعرض الموقع

## 📦 البناء والنشر

### بناء المشروع للإنتاج
```bash
npm run build
```

### تشغيل النسخة المبنية
```bash
npm run start
```

### فحص الكود
```bash
npm run lint
```

## 🎯 الصفحات المتاحة

- **الصفحة الرئيسية** (`/`) - عرض شامل للشركة وخدماتها
- **من نحن** (`/about`) - معلومات عن الشركة وفريق العمل
- **تواصل معنا** (`/contact`) - نموذج التواصل ومعلومات الاتصال
- **تطوير المواقع** (`/services/web-development`) - خدمة تطوير المواقع والمتاجر
- **تطوير التطبيقات** (`/services/mobile-development`) - خدمة تطوير تطبيقات الموبايل
- **برنامج dash** (`/programs/dash`) - لوحة التحكم الشاملة

## 🎨 نظام الألوان

```css
/* الألوان الأساسية */
--primary: #1e40af;      /* الأزرق الأساسي */
--primary-600: #1d4ed8;  /* الأزرق الداكن */
--accent: #f59e0b;       /* البرتقالي المميز */
--accent-600: #d97706;   /* البرتقالي الداكن */

/* التدرجات */
.gradient-primary {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}
```

## 📱 التجاوب

الموقع مصمم ليعمل بشكل مثالي على جميع الأجهزة:
- 📱 الهواتف الذكية (320px+)
- 📱 الأجهزة اللوحية (768px+)
- 💻 أجهزة الكمبيوتر المحمولة (1024px+)
- 🖥️ الشاشات الكبيرة (1280px+)

## 🔧 التخصيص

### إضافة صفحة جديدة
1. أنشئ مجلد جديد في `src/app/`
2. أضف ملف `page.tsx` داخل المجلد
3. استخدم نفس هيكل الصفحات الموجودة

### تعديل الألوان
عدّل ملف `tailwind.config.ts` لتخصيص نظام الألوان

### إضافة مكون جديد
أضف المكونات الجديدة في مجلد `src/components/`

## 📞 معلومات الاتصال

- **البريد الإلكتروني**: <EMAIL>
- **الإدارة**: <EMAIL>
- **الهاتف (مصر)**: +201066170179
- **الهاتف (بريطانيا)**: +44 7477 211088

## 📄 الترخيص

© 2025 Syriana Software. جميع الحقوق محفوظة.

علامة تجارية مسجلة تابعة لشركة SouqProcare LTD (المملكة المتحدة) و Souq Pro Care.Com (مصر).

---

**تم التطوير بواسطة فريق Syriana Software** 🚀
