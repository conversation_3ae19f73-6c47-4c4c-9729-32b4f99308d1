const withNextIntl = require('next-intl/plugin')(
  // This is the default location for the i18n config
  './src/i18n/config.ts'
);

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost'],
  },
  distDir: '.next',
  generateEtags: false,
  poweredByHeader: false,
  compress: true,
  experimental: {
    forceSwcTransforms: true,
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
  // Ensure proper locale handling
  trailingSlash: false,
}

module.exports = withNextIntl(nextConfig)
