import { getRequestConfig } from 'next-intl/server'

// Can be imported from a shared config
export const locales = ['ar', 'en'] as const
export type Locale = typeof locales[number]

export const defaultLocale: Locale = 'ar'

export default getRequestConfig(async ({ locale }) => {
  // Validate locale parameter
  const validLocale = locale && locales.includes(locale as any) ? locale : defaultLocale

  console.log('getRequestConfig - input locale:', locale, 'resolved to:', validLocale)

  try {
    const messages = (await import(`../messages/${validLocale}.json`)).default
    return {
      messages,
      locale: validLocale
    }
  } catch (error) {
    console.error(`Failed to load messages for locale: ${validLocale}`, error)
    // Fallback to default locale messages
    const fallbackMessages = (await import(`../messages/${defaultLocale}.json`)).default
    return {
      messages: fallbackMessages,
      locale: defaultLocale
    }
  }
})

// Currency configuration
export const currencies = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'EGP', symbol: 'ج.م', name: 'Egyptian Pound' },
  { code: 'SAR', symbol: 'ر.س', name: 'Saudi Riyal' },
  { code: 'QAR', symbol: 'ر.ق', name: 'Qatari Riyal' },
  { code: 'AED', symbol: 'د.إ', name: 'UAE Dirham' },
  { code: 'SYP', symbol: 'ل.س', name: 'Syrian Pound' }
] as const

export type Currency = typeof currencies[number]
export const defaultCurrency: Currency = currencies[0] // USD

// Language configuration
export const languages = [
  { code: 'ar', name: 'العربية', dir: 'rtl' },
  { code: 'en', name: 'English', dir: 'ltr' }
] as const

export type Language = typeof languages[number]
