'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

const CTASection = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const t = useTranslations('sections.cta')
  const tContact = useTranslations('contact')

  return (
    <section ref={ref} className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 gradient-primary"></div>
      
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 border border-white/30 rounded-full"></div>
        <div className="absolute top-32 right-20 w-16 h-16 border border-white/30 rounded-full"></div>
        <div className="absolute bottom-20 left-1/4 w-24 h-24 border border-white/30 rounded-full"></div>
        <div className="absolute bottom-10 right-10 w-18 h-18 border border-white/30 rounded-full"></div>
        
        {/* Floating Elements */}
        <motion.div
          animate={{ y: [0, -20, 0] }}
          transition={{ duration: 4, repeat: Infinity }}
          className="absolute top-20 right-1/4 w-12 h-12 bg-white/10 rounded-lg"
        ></motion.div>
        <motion.div
          animate={{ y: [0, 20, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
          className="absolute bottom-32 left-1/3 w-8 h-8 bg-accent/20 rounded-full"
        ></motion.div>
      </div>

      <div className="relative z-10 container mx-auto px-4 text-center text-white">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto"
        >
          {/* Main Heading */}
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight"
          >
            {t('title')}
            <span className="block text-accent">{t('titleHighlight')}</span>
          </motion.h2>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto"
          >
            {t('subtitle')}
          </motion.p>

          {/* Features Grid */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="text-3xl mb-3">⚡</div>
              <h3 className="font-semibold mb-2">{t('features.fastDevelopment.title')}</h3>
              <p className="text-sm text-gray-300">{t('features.fastDevelopment.description')}</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="text-3xl mb-3">🛡️</div>
              <h3 className="font-semibold mb-2">{t('features.advancedSecurity.title')}</h3>
              <p className="text-sm text-gray-300">{t('features.advancedSecurity.description')}</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="text-3xl mb-3">📞</div>
              <h3 className="font-semibold mb-2">{t('features.continuousSupport.title')}</h3>
              <p className="text-sm text-gray-300">{t('features.continuousSupport.description')}</p>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <Link
              href="/contact"
              className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 hover:transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              {t('buttons.contactExpert')}
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
            >
              {t('buttons.viewPortfolio')}
            </Link>
          </motion.div>

          {/* Contact Info */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 1 }}
            className="border-t border-white/20 pt-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="text-center">
                <div className="text-accent font-semibold mb-1">{t('contact.email')}</div>
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="text-center">
                <div className="text-accent font-semibold mb-1">{t('contact.phoneEgypt')}</div>
                <a href="tel:+201066170179" className="text-gray-300 hover:text-accent transition-colors">
                  +201066170179
                </a>
              </div>
              <div className="text-center">
                <div className="text-accent font-semibold mb-1">{t('contact.phoneUK')}</div>
                <a href="tel:+447477211088" className="text-gray-300 hover:text-accent transition-colors">
                  +44 7477 211088
                </a>
              </div>
            </div>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="mt-12 pt-8 border-t border-white/20"
          >
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-300">
              <div className="flex items-center gap-2">
                <span className="text-accent">✓</span>
                <span>{t('trustIndicators.freeConsultation')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-accent">✓</span>
                <span>{t('trustIndicators.qualityGuarantee')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-accent">✓</span>
                <span>{t('trustIndicators.postDeliverySupport')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-accent">✓</span>
                <span>{t('trustIndicators.competitivePrices')}</span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default CTASection
