'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'

const JourneySection = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const t = useTranslations('sections.journey')
  const tButtons = useTranslations('buttons')

  const steps = [
    {
      number: "01",
      title: t('phases.consultation.title'),
      description: t('phases.consultation.description'),
      details: t.raw('phases.consultation.details'),
      icon: "🎯",
      color: "from-blue-500 to-blue-600"
    },
    {
      number: "02",
      title: t('phases.design.title'),
      description: t('phases.design.description'),
      details: t.raw('phases.design.details'),
      icon: "⚡",
      color: "from-green-500 to-green-600"
    },
    {
      number: "03",
      title: t('phases.launch.title'),
      description: t('phases.launch.description'),
      details: t.raw('phases.launch.details'),
      icon: "🚀",
      color: "from-purple-500 to-purple-600"
    }
  ]

  return (
    <section ref={ref} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-6">
            {t('title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('subtitle')}
          </p>
        </motion.div>

        <div className="relative">
          {/* Connection Line */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-green-500 to-purple-500 transform -translate-y-1/2 z-0"></div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 relative z-10">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
                transition={{ duration: 0.8, delay: index * 0.3 }}
                className="relative"
              >
                {/* Step Card */}
                <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift">
                  {/* Step Number */}
                  <div className="relative">
                    <div className={`bg-gradient-to-r ${step.color} p-6 text-white`}>
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-4xl">{step.icon}</div>
                        <div className="text-2xl font-bold opacity-50">{step.number}</div>
                      </div>
                      <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                      <p className="text-white/90 text-sm leading-relaxed">
                        {step.description}
                      </p>
                    </div>
                  </div>

                  {/* Step Details */}
                  <div className="p-6">
                    <h4 className="font-semibold text-gray-800 mb-4">{t('phases.consultation.whatWeDo')}</h4>
                    <ul className="space-y-3">
                      {step.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start">
                          <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                          <span className="text-gray-600 text-sm">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Connection Arrow (Mobile) */}
                {index < steps.length - 1 && (
                  <div className="lg:hidden flex justify-center my-6">
                    <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center text-white">
                      ↓
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Timeline Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="mt-16 bg-white rounded-2xl shadow-lg p-8"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-primary mb-2">
              متوسط أوقات التسليم
            </h3>
            <p className="text-gray-600">
              نلتزم بالمواعيد المحددة ونسعى لتسليم مشاريع عالية الجودة في الوقت المناسب
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-500 mb-2">1-2 أسابيع</div>
              <div className="text-gray-600">المواقع البسيطة</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-500 mb-2">4-8 أسابيع</div>
              <div className="text-gray-600">المتاجر الإلكترونية</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-500 mb-2">8-16 أسبوع</div>
              <div className="text-gray-600">الأنظمة المعقدة</div>
            </div>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary to-primary-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              {t('readyToStart')}
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              {t('readyDescription')}
            </p>
            <Link
              href="/contact"
              className="bg-accent hover:bg-accent-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300 inline-block"
            >
              {tButtons('getStarted')}
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default JourneySection
