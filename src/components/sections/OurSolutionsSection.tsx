'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

const OurSolutionsSection = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const t = useTranslations('sections.ourSolutions')
  const tServices = useTranslations('services')
  const tPrograms = useTranslations('programs')
  const tPartners = useTranslations('partners')

  const solutionCategories = [
    {
      title: t('categories.commerce.title'),
      description: t('categories.commerce.description'),
      icon: "💼",
      color: "from-blue-500 to-blue-600",
      solutions: [
        {
          name: tServices('webDevelopment.name'),
          description: tServices('webDevelopment.description'),
          link: "/services/web-development"
        },
        {
          name: tPrograms('sybooking.name'),
          description: tPrograms('sybooking.description'),
          link: "/programs/sybooking"
        },
        {
          name: tPrograms('dash.name'),
          description: tPrograms('dash.description'),
          link: "/programs/dash"
        }
      ]
    },
    {
      title: t('categories.growth.title'),
      description: t('categories.growth.description'),
      icon: "📈",
      color: "from-green-500 to-green-600",
      solutions: [
        {
          name: tServices('saasGrowth.name'),
          description: tServices('saasGrowth.description'),
          link: "/services/saas-growth"
        },
        {
          name: tServices('automation.name'),
          description: tServices('automation.description'),
          link: "/services/automation"
        },
        {
          name: tPrograms('syriana-bot.name'),
          description: tPrograms('syriana-bot.description'),
          link: "/programs/syriana-bot"
        },
        {
          name: tPrograms('ease.name'),
          description: tPrograms('ease.description'),
          link: "/programs/ease"
        }
      ]
    },
    {
      title: t('categories.infrastructure.title'),
      description: t('categories.infrastructure.description'),
      icon: "🏗️",
      color: "from-purple-500 to-purple-600",
      solutions: [
        {
          name: tServices('hosting.name'),
          description: tServices('hosting.description'),
          link: "/services/hosting"
        },
        {
          name: tPrograms('sylink.name'),
          description: tPrograms('sylink.description'),
          link: "/programs/sylink"
        },
        {
          name: tPartners('reseller'),
          description: tPartners('resellerDescription'),
          link: "/partners/reseller"
        },
        {
          name: tPartners('openSource'),
          description: tPartners('openSourceDescription'),
          link: "/open-source"
        }
      ]
    }
  ]

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-6">
            {t('title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('subtitle')}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {solutionCategories.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: categoryIndex * 0.2 }}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
            >
              {/* Category Header */}
              <div className={`bg-gradient-to-r ${category.color} p-6 text-white`}>
                <div className="text-4xl mb-4">{category.icon}</div>
                <h3 className="text-xl font-bold mb-2">{category.title}</h3>
                <p className="text-white/90 text-sm leading-relaxed">
                  {category.description}
                </p>
              </div>

              {/* Solutions List */}
              <div className="p-6">
                <div className="space-y-4">
                  {category.solutions.map((solution, solutionIndex) => (
                    <Link
                      key={solutionIndex}
                      href={solution.link}
                      className="block p-4 rounded-lg border border-gray-100 hover:border-accent hover:bg-accent/5 transition-all duration-300 group"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-800 group-hover:text-accent transition-colors duration-300 mb-1">
                            {solution.name}
                          </h4>
                          <p className="text-gray-600 text-sm">
                            {solution.description}
                          </p>
                        </div>
                        <div className="text-accent opacity-0 group-hover:opacity-100 transition-opacity duration-300 mr-2">
                          ←
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>

                {/* Category CTA */}
                <div className="mt-6 pt-6 border-t border-gray-100">
                  <button className="w-full bg-gray-100 hover:bg-accent hover:text-white text-gray-700 py-3 rounded-lg font-semibold transition-all duration-300">
                    {t('exploreAll')}
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-primary to-primary-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              {t('customSolutions.title')}
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              {t('customSolutions.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300"
              >
                {t('customSolutions.requestCustom')}
              </Link>
              <Link
                href="/portfolio"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                {t('customSolutions.viewPortfolio')}
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default OurSolutionsSection
