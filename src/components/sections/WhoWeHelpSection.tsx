'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'

const WhoWeHelpSection = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const t = useTranslations('sections.whoWeHelp')
  const tButtons = useTranslations('buttons')

  const cards = [
    {
      title: t('ecommerce.title'),
      description: t('ecommerce.description'),
      features: t.raw('ecommerce.features'),
      icon: "🛒",
      color: "from-blue-500 to-blue-600"
    },
    {
      title: t('saas.title'),
      description: t('saas.description'),
      features: t.raw('saas.features'),
      icon: "🚀",
      color: "from-green-500 to-green-600"
    },
    {
      title: t('agencies.title'),
      description: t('agencies.description'),
      features: t.raw('agencies.features'),
      icon: "🤝",
      color: "from-purple-500 to-purple-600"
    }
  ]

  return (
    <section ref={ref} className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary mb-6">
            {t('title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('subtitle')}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {cards.map((card, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift"
            >
              {/* Card Header */}
              <div className={`bg-gradient-to-r ${card.color} p-6 text-white`}>
                <div className="text-4xl mb-4">{card.icon}</div>
                <h3 className="text-xl font-bold mb-2">{card.title}</h3>
                <p className="text-white/90 text-sm leading-relaxed">
                  {card.description}
                </p>
              </div>

              {/* Card Body */}
              <div className="p-6">
                <h4 className="font-semibold text-gray-800 mb-4">ما نقدمه لك:</h4>
                <ul className="space-y-3">
                  {card.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Card Footer */}
              <div className="px-6 pb-6">
                <button className="w-full bg-primary hover:bg-primary-600 text-white py-3 rounded-lg font-semibold transition-colors duration-300">
                  تعرف على المزيد
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-primary mb-4">
              {t('notSure.title')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('notSure.description')}
            </p>
            <Link href="/contact" className="inline-block bg-accent hover:bg-accent-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300">
              {t('notSure.cta')}
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default WhoWeHelpSection
