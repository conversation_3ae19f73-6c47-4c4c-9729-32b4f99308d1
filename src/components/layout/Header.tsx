'use client'

import { useState } from 'react'
import Link from 'next/link'
import { ChevronDownIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { useTranslations } from 'next-intl'
import LanguageSelector from '@/components/ui/LanguageSelector'
import CurrencySelector from '@/components/ui/CurrencySelector'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)
  const t = useTranslations('navigation')
  const tPrograms = useTranslations('programs')
  const tServices = useTranslations('services')
  const tPartners = useTranslations('partners')
  const tCompany = useTranslations('company')

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)

  const handleDropdown = (menu: string) => {
    setActiveDropdown(activeDropdown === menu ? null : menu)
  }

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 space-x-reverse">
            <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">S</span>
            </div>
            <span className="text-xl font-bold text-primary">Syriana Software</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            <Link href="/" className="text-gray-700 hover:text-primary transition-colors">
              {t('home')}
            </Link>
            
            {/* برامجنا Mega Menu */}
            <div className="relative group">
              <button
                className="flex items-center text-gray-700 hover:text-primary transition-colors"
                onClick={() => handleDropdown('programs')}
              >
                {t('programs')}
                <ChevronDownIcon className="w-4 h-4 mr-1" />
              </button>
              <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                <div className="p-6">
                  <div className="grid grid-cols-1 gap-4">
                    <Link href="/programs/dash" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">D</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tPrograms('dash.name')}</h3>
                        <p className="text-sm text-gray-600">{tPrograms('dash.title')}</p>
                      </div>
                    </Link>
                    <Link href="/programs/sybooking" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">B</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tPrograms('sybooking.name')}</h3>
                        <p className="text-sm text-gray-600">{tPrograms('sybooking.title')}</p>
                      </div>
                    </Link>
                    <Link href="/programs/sylink" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">L</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tPrograms('sylink.name')}</h3>
                        <p className="text-sm text-gray-600">{tPrograms('sylink.title')}</p>
                      </div>
                    </Link>
                    <Link href="/programs/ease" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">E</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tPrograms('ease.name')}</h3>
                        <p className="text-sm text-gray-600">{tPrograms('ease.title')}</p>
                      </div>
                    </Link>
                    <Link href="/programs/syriana-bot" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">S</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tPrograms('syriana-bot.name')}</h3>
                        <p className="text-sm text-gray-600">{tPrograms('syriana-bot.title')}</p>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* خدماتنا Mega Menu */}
            <div className="relative group">
              <button
                className="flex items-center text-gray-700 hover:text-primary transition-colors"
                onClick={() => handleDropdown('services')}
              >
                {t('services')}
                <ChevronDownIcon className="w-4 h-4 mr-1" />
              </button>
              <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                <div className="p-6">
                  <div className="grid grid-cols-1 gap-4">
                    <Link href="/services/web-development" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">W</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tServices('webDevelopment.name')}</h3>
                        <p className="text-sm text-gray-600">{tServices('webDevelopment.description')}</p>
                      </div>
                    </Link>
                    <Link href="/services/mobile-development" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">M</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tServices('mobileDevelopment.name')}</h3>
                        <p className="text-sm text-gray-600">{tServices('mobileDevelopment.description')}</p>
                      </div>
                    </Link>
                    <Link href="/services/hosting" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">H</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tServices('hosting.name')}</h3>
                        <p className="text-sm text-gray-600">{tServices('hosting.description')}</p>
                      </div>
                    </Link>
                    <Link href="/services/automation" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">A</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tServices('automation.name')}</h3>
                        <p className="text-sm text-gray-600">{tServices('automation.description')}</p>
                      </div>
                    </Link>
                    <Link href="/services/saas-growth" className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center ml-3">
                        <span className="text-accent font-bold">S</span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{tServices('saasGrowth.name')}</h3>
                        <p className="text-sm text-gray-600">{tServices('saasGrowth.description')}</p>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            <Link href="/store" className="text-gray-700 hover:text-primary transition-colors">
              {t('store')}
            </Link>

            <Link href="/open-source" className="text-gray-700 hover:text-primary transition-colors">
              {t('openSource')}
            </Link>

            {/* شركاؤنا Dropdown */}
            <div className="relative group">
              <button className="flex items-center text-gray-700 hover:text-primary transition-colors">
                {t('partners')}
                <ChevronDownIcon className="w-4 h-4 mr-1" />
              </button>
              <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                <div className="py-2">
                  <Link href="/partners/recommended" className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors">
                    {tPartners('recommended')}
                  </Link>
                  <Link href="/partners/affiliate" className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors">
                    {tPartners('affiliate')}
                  </Link>
                  <Link href="/partners/reseller" className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors">
                    {tPartners('reseller')}
                  </Link>
                </div>
              </div>
            </div>

            {/* عن الشركة Dropdown */}
            <div className="relative group">
              <button className="flex items-center text-gray-700 hover:text-primary transition-colors">
                {t('about')}
                <ChevronDownIcon className="w-4 h-4 mr-1" />
              </button>
              <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
                <div className="py-2">
                  <Link href="/about" className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors">
                    {t('about')}
                  </Link>
                  <Link href="/portfolio" className="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors">
                    {t('portfolio')}
                  </Link>
                </div>
              </div>
            </div>

            <Link href="/contact" className="text-gray-700 hover:text-primary transition-colors">
              {t('contact')}
            </Link>
          </nav>

          {/* Language and Currency Selectors */}
          <div className="hidden lg:flex items-center space-x-4 space-x-reverse">
            <LanguageSelector />
            <CurrencySelector />
          </div>

          {/* Mobile menu button */}
          <button
            onClick={toggleMenu}
            className="lg:hidden p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-100 transition-colors"
          >
            {isMenuOpen ? (
              <XMarkIcon className="w-6 h-6" />
            ) : (
              <Bars3Icon className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200">
            <div className="space-y-2">
              <Link href="/" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('home')}
              </Link>
              <Link href="/programs" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('programs')}
              </Link>
              <Link href="/services" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('services')}
              </Link>
              <Link href="/store" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('store')}
              </Link>
              <Link href="/open-source" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('openSource')}
              </Link>
              <Link href="/partners" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('partners')}
              </Link>
              <Link href="/about" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('about')}
              </Link>
              <Link href="/contact" className="block py-2 text-gray-700 hover:text-primary transition-colors">
                {t('contact')}
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
