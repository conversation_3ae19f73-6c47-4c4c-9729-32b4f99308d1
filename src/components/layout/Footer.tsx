'use client'

import Link from 'next/link'
import { useTranslations } from 'next-intl'

const Footer = () => {
  const t = useTranslations('navigation')
  const tFooter = useTranslations('footer')
  const tServices = useTranslations('services')
  const tPrograms = useTranslations('programs')
  const tContact = useTranslations('contact')
  const tPartners = useTranslations('partners')
  const tCompany = useTranslations('company')
  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* معلومات الشركة */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">S</span>
              </div>
              <span className="text-lg font-bold">{tCompany('name')}</span>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              {tFooter('description')}
            </p>
            <div className="text-sm text-gray-400">
              <p>{tFooter('trademark')}</p>
              <p>{tFooter('ukCompany')}</p>
              <p>{tFooter('egyptCompany')}</p>
            </div>
          </div>

          {/* الخدمات */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-accent">{t('services')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/services/web-development" className="text-gray-300 hover:text-accent transition-colors">
                  {tServices('webDevelopment.name')}
                </Link>
              </li>
              <li>
                <Link href="/services/mobile-development" className="text-gray-300 hover:text-accent transition-colors">
                  {tServices('mobileDevelopment.name')}
                </Link>
              </li>
              <li>
                <Link href="/services/hosting" className="text-gray-300 hover:text-accent transition-colors">
                  {tServices('hosting.name')}
                </Link>
              </li>
              <li>
                <Link href="/services/automation" className="text-gray-300 hover:text-accent transition-colors">
                  {tServices('automation.name')}
                </Link>
              </li>
              <li>
                <Link href="/services/saas-growth" className="text-gray-300 hover:text-accent transition-colors">
                  {tServices('saasGrowth.name')}
                </Link>
              </li>
            </ul>
          </div>

          {/* البرامج */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-accent">{t('programs')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/programs/dash" className="text-gray-300 hover:text-accent transition-colors">
                  {tPrograms('dash.name')}
                </Link>
              </li>
              <li>
                <Link href="/programs/sybooking" className="text-gray-300 hover:text-accent transition-colors">
                  {tPrograms('sybooking.name')}
                </Link>
              </li>
              <li>
                <Link href="/programs/sylink" className="text-gray-300 hover:text-accent transition-colors">
                  {tPrograms('sylink.name')}
                </Link>
              </li>
              <li>
                <Link href="/programs/ease" className="text-gray-300 hover:text-accent transition-colors">
                  {tPrograms('ease.name')}
                </Link>
              </li>
              <li>
                <Link href="/programs/syriana-bot" className="text-gray-300 hover:text-accent transition-colors">
                  {tPrograms('syriana-bot.name')}
                </Link>
              </li>
            </ul>
          </div>

          {/* معلومات الاتصال */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-accent">{t('contact')}</h3>
            <div className="space-y-3 text-sm">
              <div>
                <p className="text-gray-400">{tContact('info.email')}:</p>
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div>
                <p className="text-gray-400">{tContact('info.management')}:</p>
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div>
                <p className="text-gray-400">{tContact('info.phone')}:</p>
                <div className="space-y-1">
                  <p className="text-gray-300">
                    <span className="text-gray-400">{tContact('info.egypt')}:</span> +201066170179
                  </p>
                  <p className="text-gray-300">
                    <span className="text-gray-400">{tContact('info.egypt')}:</span> +201025800940
                  </p>
                  <p className="text-gray-300">
                    <span className="text-gray-400">{tContact('info.uk')}:</span> +44 7477 211088
                  </p>
                  <p className="text-gray-300">
                    <span className="text-gray-400">{tContact('info.landline')}:</span> +20 57 2058006
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* الروابط السريعة */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-wrap justify-center gap-6 text-sm">
            <Link href="/about" className="text-gray-300 hover:text-accent transition-colors">
              {t('about')}
            </Link>
            <Link href="/portfolio" className="text-gray-300 hover:text-accent transition-colors">
              {t('portfolio')}
            </Link>
            <Link href="/open-source" className="text-gray-300 hover:text-accent transition-colors">
              {t('openSource')}
            </Link>
            <Link href="/store" className="text-gray-300 hover:text-accent transition-colors">
              {t('store')}
            </Link>
            <Link href="/partners/affiliate" className="text-gray-300 hover:text-accent transition-colors">
              {tPartners('affiliate')}
            </Link>
            <Link href="/partners/reseller" className="text-gray-300 hover:text-accent transition-colors">
              {tPartners('reseller')}
            </Link>
            <Link href="/contact" className="text-gray-300 hover:text-accent transition-colors">
              {t('contact')}
            </Link>
          </div>
        </div>

        {/* حقوق النشر */}
        <div className="border-t border-gray-700 mt-8 pt-8 text-center">
          <p className="text-gray-400 text-sm">
            {tFooter('copyright')}
          </p>
          <p className="text-gray-500 text-xs mt-2">
            {tFooter('copyrightFull')}
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
