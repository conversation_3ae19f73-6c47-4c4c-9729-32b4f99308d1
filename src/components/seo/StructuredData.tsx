'use client'

import { useLocale, useTranslations } from 'next-intl'

interface StructuredDataProps {
  type?: 'website' | 'organization' | 'product' | 'service'
  data?: Record<string, any>
}

export default function StructuredData({ type = 'website', data = {} }: StructuredDataProps) {
  const locale = useLocale()
  const t = useTranslations('company')
  const tMeta = useTranslations('meta')

  const baseUrl = 'https://syriana.software'
  const isArabic = locale === 'ar'

  const getStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': type === 'website' ? 'WebSite' : 'Organization',
      name: 'Syriana Software',
      alternateName: 'سيريانا سوفتوير',
      url: baseUrl,
      logo: `${baseUrl}/images/logo.png`,
      description: tMeta('defaultDescription'),
      inLanguage: [
        {
          '@type': 'Language',
          name: 'Arabic',
          alternateName: 'ar'
        },
        {
          '@type': 'Language', 
          name: 'English',
          alternateName: 'en'
        }
      ],
      sameAs: [
        'https://www.linkedin.com/company/syriana-software',
        'https://twitter.com/syriana_software',
        'https://github.com/syriana-software'
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+201066170179',
        contactType: 'customer service',
        areaServed: ['EG', 'SA', 'AE', 'QA', 'GB'],
        availableLanguage: ['Arabic', 'English']
      },
      address: [
        {
          '@type': 'PostalAddress',
          addressCountry: 'EG',
          addressLocality: 'Cairo',
          addressRegion: 'Cairo'
        },
        {
          '@type': 'PostalAddress',
          addressCountry: 'GB',
          addressLocality: 'London'
        }
      ],
      foundingDate: '2020',
      numberOfEmployees: {
        '@type': 'QuantitativeValue',
        minValue: 10,
        maxValue: 50
      },
      ...data
    }

    if (type === 'website') {
      return {
        ...baseData,
        '@type': 'WebSite',
        potentialAction: {
          '@type': 'SearchAction',
          target: `${baseUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string'
        }
      }
    }

    return baseData
  }

  const structuredData = getStructuredData()

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  )
}
