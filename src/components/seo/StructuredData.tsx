interface StructuredDataProps {
  type?: 'website' | 'organization' | 'product' | 'service'
  data?: Record<string, any>
  locale?: string
}

export default function StructuredData({ type = 'website', data = {}, locale = 'ar' }: StructuredDataProps) {

  const baseUrl = 'https://syriana.software'
  const isArabic = locale === 'ar'

  const getStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': type === 'website' ? 'WebSite' : 'Organization',
      name: 'Syriana Software',
      alternateName: 'سيريانا سوفتوير',
      url: baseUrl,
      logo: `${baseUrl}/images/logo.png`,
      description: isArabic
        ? 'نحن لا نبني برامج فقط، بل نبني جسورًا لنجاحك. من الفكرة إلى الإطلاق وما بعده، نوفر لك جميع الأدوات والخبرات التي تحتاجها للتفوق في العالم الرقمي.'
        : 'We don\'t just build software, we build bridges to your success. From idea to launch and beyond, we provide you with all the tools and expertise you need to excel in the digital world.',
      inLanguage: [
        {
          '@type': 'Language',
          name: 'Arabic',
          alternateName: 'ar'
        },
        {
          '@type': 'Language', 
          name: 'English',
          alternateName: 'en'
        }
      ],
      sameAs: [
        'https://www.linkedin.com/company/syriana-software',
        'https://twitter.com/syriana_software',
        'https://github.com/syriana-software'
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+201066170179',
        contactType: 'customer service',
        areaServed: ['EG', 'SA', 'AE', 'QA', 'GB'],
        availableLanguage: ['Arabic', 'English']
      },
      address: [
        {
          '@type': 'PostalAddress',
          addressCountry: 'EG',
          addressLocality: 'Cairo',
          addressRegion: 'Cairo'
        },
        {
          '@type': 'PostalAddress',
          addressCountry: 'GB',
          addressLocality: 'London'
        }
      ],
      foundingDate: '2020',
      numberOfEmployees: {
        '@type': 'QuantitativeValue',
        minValue: 10,
        maxValue: 50
      },
      ...data
    }

    if (type === 'website') {
      return {
        ...baseData,
        '@type': 'WebSite',
        potentialAction: {
          '@type': 'SearchAction',
          target: `${baseUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string'
        }
      }
    }

    return baseData
  }

  const structuredData = getStructuredData()

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  )
}
