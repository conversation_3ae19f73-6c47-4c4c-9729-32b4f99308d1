'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { ChevronDownIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline'
import { useCurrency } from '@/contexts/LocaleContext'

export default function CurrencySelector() {
  const [isOpen, setIsOpen] = useState(false)
  const { currency, setCurrency, currencies } = useCurrency()
  const t = useTranslations('currency')

  const handleCurrencyChange = (newCurrency: any) => {
    setCurrency(newCurrency)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary transition-colors duration-200 rounded-lg hover:bg-gray-100"
        aria-label={t('label')}
      >
        <CurrencyDollarIcon className="w-4 h-4" />
        <span>{currency.code}</span>
        <ChevronDownIcon 
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 z-20">
            <div className="py-2">
              {currencies.map((curr) => (
                <button
                  key={curr.code}
                  onClick={() => handleCurrencyChange(curr)}
                  className={`w-full text-right px-4 py-2 text-sm hover:bg-gray-50 transition-colors duration-200 flex items-center justify-between ${
                    currency.code === curr.code 
                      ? 'bg-accent/10 text-accent font-semibold' 
                      : 'text-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                      {curr.symbol}
                    </span>
                    <span>{curr.name}</span>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-xs text-gray-500">{curr.code}</span>
                    {currency.code === curr.code && (
                      <div className="w-2 h-2 bg-accent rounded-full" />
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
