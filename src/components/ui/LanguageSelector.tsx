'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { ChevronDownIcon, LanguageIcon } from '@heroicons/react/24/outline'
import { useLanguage } from '@/contexts/LocaleContext'
import { languages } from '@/i18n/config'

export default function LanguageSelector() {
  const [isOpen, setIsOpen] = useState(false)
  const { locale, switchLanguage } = useLanguage()
  const t = useTranslations('language')

  const currentLanguage = languages.find(lang => lang.code === locale)

  const handleLanguageChange = (newLocale: string) => {
    switchLanguage(newLocale as any)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary transition-colors duration-200 rounded-lg hover:bg-gray-100"
        aria-label={t('label')}
      >
        <LanguageIcon className="w-4 h-4" />
        <span>{currentLanguage?.name}</span>
        <ChevronDownIcon 
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-20">
            <div className="py-2">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageChange(language.code)}
                  className={`w-full text-right px-4 py-2 text-sm hover:bg-gray-50 transition-colors duration-200 flex items-center justify-between ${
                    locale === language.code 
                      ? 'bg-accent/10 text-accent font-semibold' 
                      : 'text-gray-700'
                  }`}
                >
                  <span>{language.name}</span>
                  {locale === language.code && (
                    <div className="w-2 h-2 bg-accent rounded-full" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
