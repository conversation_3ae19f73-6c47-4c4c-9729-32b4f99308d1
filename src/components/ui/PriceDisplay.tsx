'use client'

import { useEffect, useState } from 'react'
import { useCurrency } from '@/contexts/LocaleContext'

interface PriceDisplayProps {
  amount: number
  originalCurrency?: string
  className?: string
  showCurrency?: boolean
  showOriginal?: boolean
}

export default function PriceDisplay({
  amount,
  originalCurrency = 'USD',
  className = '',
  showCurrency = true,
  showOriginal = false
}: PriceDisplayProps) {
  const { currency, convertPrice, formatPrice } = useCurrency()
  const [convertedAmount, setConvertedAmount] = useState(amount)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const performConversion = async () => {
      if (originalCurrency === currency.code) {
        setConvertedAmount(amount)
        return
      }

      setIsLoading(true)
      try {
        const converted = convertPrice(amount, originalCurrency)
        setConvertedAmount(converted)
      } catch (error) {
        console.error('Price conversion error:', error)
        setConvertedAmount(amount)
      } finally {
        setIsLoading(false)
      }
    }

    performConversion()
  }, [amount, originalCurrency, currency.code, convertPrice])

  if (isLoading) {
    return (
      <span className={`inline-block animate-pulse bg-gray-200 rounded h-5 w-16 ${className}`} />
    )
  }

  const formattedPrice = showCurrency ? formatPrice(convertedAmount) : convertedAmount.toString()
  const originalPrice = showCurrency ? formatPrice(amount) : amount.toString()

  return (
    <span className={className}>
      {formattedPrice}
      {showOriginal && originalCurrency !== currency.code && (
        <span className="text-xs text-gray-500 ml-2">
          (≈ {originalPrice} {originalCurrency})
        </span>
      )}
    </span>
  )
}

// Hook for manual price conversion
export function usePrice() {
  const { convertPrice, formatPrice, currency } = useCurrency()

  const convertAndFormat = async (
    amount: number,
    fromCurrency: string = 'USD'
  ): Promise<string> => {
    try {
      const converted = convertPrice(amount, fromCurrency)
      return formatPrice(converted)
    } catch (error) {
      console.error('Price conversion error:', error)
      return formatPrice(amount)
    }
  }

  return {
    convertAndFormat,
    formatPrice,
    currency
  }
}
