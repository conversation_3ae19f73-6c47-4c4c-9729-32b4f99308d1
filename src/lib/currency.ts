import { Currency, currencies, defaultCurrency } from '@/i18n/config'

// Exchange rates cache
let exchangeRatesCache: { [key: string]: number } | null = null
let lastFetchTime = 0
const CACHE_DURATION = 60 * 60 * 1000 // 1 hour in milliseconds

// Free exchange rate API (no API key required)
const EXCHANGE_API_URL = 'https://api.exchangerate-api.com/v4/latest/USD'

export interface ExchangeRates {
  [currencyCode: string]: number
}

export async function fetchExchangeRates(): Promise<ExchangeRates> {
  const now = Date.now()
  
  // Return cached rates if they're still fresh
  if (exchangeRatesCache && (now - lastFetchTime) < CACHE_DURATION) {
    return exchangeRatesCache
  }

  try {
    const response = await fetch(EXCHANGE_API_URL, {
      next: { revalidate: 3600 } // Cache for 1 hour
    })
    
    if (!response.ok) {
      throw new Error('Failed to fetch exchange rates')
    }
    
    const data = await response.json()
    
    // The API returns rates relative to USD
    const rates: ExchangeRates = {
      USD: 1, // Base currency
      ...data.rates
    }
    
    // Update cache
    exchangeRatesCache = rates
    lastFetchTime = now
    
    return rates
  } catch (error) {
    console.error('Error fetching exchange rates:', error)
    
    // Return fallback rates if API fails
    return getFallbackRates()
  }
}

function getFallbackRates(): ExchangeRates {
  // Fallback exchange rates (approximate values)
  return {
    USD: 1,
    GBP: 0.79,
    EGP: 49.5,
    SAR: 3.75,
    QAR: 3.64,
    AED: 3.67,
    SYP: 2512
  }
}

export function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  rates: ExchangeRates
): number {
  if (fromCurrency === toCurrency) {
    return amount
  }
  
  const fromRate = rates[fromCurrency] || 1
  const toRate = rates[toCurrency] || 1
  
  // Convert to USD first, then to target currency
  const usdAmount = amount / fromRate
  const convertedAmount = usdAmount * toRate
  
  return Math.round(convertedAmount * 100) / 100 // Round to 2 decimal places
}

export function formatCurrency(
  amount: number,
  currencyCode: string,
  locale: string = 'ar'
): string {
  const currency = currencies.find(c => c.code === currencyCode) || defaultCurrency
  
  // Format number based on locale
  const formatter = new Intl.NumberFormat(locale === 'ar' ? 'ar-EG' : 'en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })
  
  const formattedAmount = formatter.format(amount)
  
  // Return formatted string with currency symbol
  if (locale === 'ar') {
    return `${formattedAmount} ${currency.symbol}`
  } else {
    return `${currency.symbol}${formattedAmount}`
  }
}

export function getCurrencyByCode(code: string): Currency | undefined {
  return currencies.find(c => c.code === code)
}

export function getAllCurrencies(): Currency[] {
  return [...currencies]
}

// Hook for client-side currency conversion
export function useCurrencyConverter() {
  const convertPrice = async (
    amount: number,
    fromCurrency: string = 'USD',
    toCurrency: string = 'USD'
  ): Promise<number> => {
    try {
      const rates = await fetchExchangeRates()
      return convertCurrency(amount, fromCurrency, toCurrency, rates)
    } catch (error) {
      console.error('Currency conversion error:', error)
      return amount // Return original amount if conversion fails
    }
  }

  const formatPrice = (
    amount: number,
    currencyCode: string,
    locale: string = 'ar'
  ): string => {
    return formatCurrency(amount, currencyCode, locale)
  }

  return {
    convertPrice,
    formatPrice,
    currencies: getAllCurrencies()
  }
}

// Server-side currency conversion for static generation
export async function getServerSideRates(): Promise<ExchangeRates> {
  try {
    return await fetchExchangeRates()
  } catch (error) {
    console.error('Server-side currency fetch error:', error)
    return getFallbackRates()
  }
}
