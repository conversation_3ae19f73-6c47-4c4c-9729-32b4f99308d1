'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { Currency, currencies, defaultCurrency, Locale, locales, defaultLocale } from '@/i18n/config'
import { ExchangeRates, fetchExchangeRates } from '@/lib/currency'

interface LocaleContextType {
  // Language
  locale: Locale
  setLocale: (locale: Locale) => void
  
  // Currency
  currency: Currency
  setCurrency: (currency: Currency) => void
  
  // Exchange rates
  exchangeRates: ExchangeRates | null
  
  // Utility functions
  switchLanguage: (newLocale: Locale) => void
  formatPrice: (amount: number) => string
  convertPrice: (amount: number, fromCurrency?: string) => number
}

const LocaleContext = createContext<LocaleContextType | undefined>(undefined)

interface LocaleProviderProps {
  children: ReactNode
  initialLocale: Locale
}

export function LocaleProvider({ children, initialLocale }: LocaleProviderProps) {
  const [locale, setLocaleState] = useState<Locale>(initialLocale)
  const [currency, setCurrencyState] = useState<Currency>(defaultCurrency)
  const [exchangeRates, setExchangeRates] = useState<ExchangeRates | null>(null)
  
  const router = useRouter()
  const pathname = usePathname()

  // Load saved preferences from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCurrency = localStorage.getItem('preferred-currency')
      if (savedCurrency) {
        const foundCurrency = currencies.find(c => c.code === savedCurrency)
        if (foundCurrency) {
          setCurrencyState(foundCurrency)
        }
      }
    }
  }, [])

  // Fetch exchange rates on mount and periodically
  useEffect(() => {
    const loadExchangeRates = async () => {
      try {
        const rates = await fetchExchangeRates()
        setExchangeRates(rates)
      } catch (error) {
        console.error('Failed to load exchange rates:', error)
      }
    }

    loadExchangeRates()
    
    // Refresh rates every hour
    const interval = setInterval(loadExchangeRates, 60 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale)
  }

  const setCurrency = (newCurrency: Currency) => {
    setCurrencyState(newCurrency)
    
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-currency', newCurrency.code)
    }
  }

  const switchLanguage = (newLocale: Locale) => {
    if (!locales.includes(newLocale)) return
    
    // Remove current locale from pathname
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    
    // Navigate to new locale
    router.push(`/${newLocale}${pathWithoutLocale}`)
  }

  const formatPrice = (amount: number): string => {
    const formatter = new Intl.NumberFormat(
      locale === 'ar' ? 'ar-EG' : 'en-US',
      {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }
    )
    
    const formattedAmount = formatter.format(amount)
    
    if (locale === 'ar') {
      return `${formattedAmount} ${currency.symbol}`
    } else {
      return `${currency.symbol}${formattedAmount}`
    }
  }

  const convertPrice = (amount: number, fromCurrency: string = 'USD'): number => {
    if (!exchangeRates || fromCurrency === currency.code) {
      return amount
    }
    
    const fromRate = exchangeRates[fromCurrency] || 1
    const toRate = exchangeRates[currency.code] || 1
    
    // Convert to USD first, then to target currency
    const usdAmount = amount / fromRate
    const convertedAmount = usdAmount * toRate
    
    return Math.round(convertedAmount * 100) / 100
  }

  const value: LocaleContextType = {
    locale,
    setLocale,
    currency,
    setCurrency,
    exchangeRates,
    switchLanguage,
    formatPrice,
    convertPrice
  }

  return (
    <LocaleContext.Provider value={value}>
      {children}
    </LocaleContext.Provider>
  )
}

export function useLocale() {
  const context = useContext(LocaleContext)
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider')
  }
  return context
}

// Hook for currency operations
export function useCurrency() {
  const { currency, setCurrency, exchangeRates, formatPrice, convertPrice } = useLocale()
  
  return {
    currency,
    setCurrency,
    exchangeRates,
    formatPrice,
    convertPrice,
    currencies
  }
}

// Hook for language operations
export function useLanguage() {
  const { locale, setLocale, switchLanguage } = useLocale()
  
  return {
    locale,
    setLocale,
    switchLanguage
  }
}
