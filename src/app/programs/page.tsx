'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const ProgramsPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">💎</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              برامجنا المتخصصة
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              مجموعة من البرامج والحلول المتخصصة التي طورناها لتلبية احتياجات مختلف القطاعات والأعمال. 
              كل برنامج مصمم بعناية ليحل مشاكل حقيقية ويحقق نتائج ملموسة.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اطلب عرض سعر
              </Link>
              <Link 
                href="#programs"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تصفح البرامج
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Programs Overview */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا برامجنا مختلفة؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نطور برامج تركز على حل المشاكل الحقيقية وتحقيق النتائج الملموسة
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "🎯",
                title: "مصممة للنتائج",
                description: "كل برنامج مصمم لحل مشكلة محددة وتحقيق أهداف واضحة"
              },
              {
                icon: "🚀",
                title: "سهولة الاستخدام",
                description: "واجهات بديهية وسهلة تجعل الاستخدام متعة وليس عبء"
              },
              {
                icon: "🔧",
                title: "قابلة للتخصيص",
                description: "يمكن تخصيص البرامج لتناسب احتياجاتك الخاصة"
              },
              {
                icon: "📈",
                title: "تطوير مستمر",
                description: "نحدث برامجنا باستمرار بناءً على ملاحظات المستخدمين"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Programs */}
      <section id="programs" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              برامجنا المتاحة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اكتشف مجموعة برامجنا المتخصصة واختر ما يناسب احتياجاتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {[
              {
                name: "dash",
                title: "لوحة التحكم الشاملة",
                description: "منصة إدارة متكاملة تجمع جميع أدوات إدارة أعمالك في مكان واحد. من المبيعات إلى المخزون والعملاء والتقارير.",
                icon: "📊",
                features: [
                  "إدارة شاملة للأعمال",
                  "تقارير وتحليلات متقدمة", 
                  "إدارة العملاء والمبيعات",
                  "تتبع المخزون والمنتجات",
                  "لوحات معلومات تفاعلية",
                  "تكاملات مع الأنظمة الأخرى"
                ],
                link: "/programs/dash",
                color: "bg-blue-500",
                badge: "الأكثر شيوعاً"
              },
              {
                name: "sybooking",
                title: "نظام الحجوزات الذكي",
                description: "منصة شاملة لإدارة الحجوزات والمواعيد تناسب جميع أنواع الأعمال من العيادات إلى صالونات التجميل.",
                icon: "📅",
                features: [
                  "حجز المواعيد أونلاين",
                  "إدارة التقويم والجدولة",
                  "تذكيرات تلقائية للعملاء",
                  "نظام دفع متكامل",
                  "إدارة الخدمات والأسعار",
                  "تقارير الحجوزات والإيرادات"
                ],
                link: "/programs/sybooking",
                color: "bg-green-500"
              },
              {
                name: "sylink",
                title: "منصة إدارة الروابط الذكية",
                description: "أداة قوية لإنشاء وإدارة وتتبع الروابط المختصرة مع تحليلات متقدمة وميزات تسويقية ذكية.",
                icon: "🔗",
                features: [
                  "اختصار الروابط المتقدم",
                  "تحليلات مفصلة للنقرات",
                  "روابط مخصصة للعلامة التجارية",
                  "استهداف جغرافي ذكي",
                  "QR Codes تلقائية",
                  "تكاملات مع أدوات التسويق"
                ],
                link: "/programs/sylink",
                color: "bg-purple-500"
              },
              {
                name: "ease",
                title: "منصة إدارة المشاريع السهلة",
                description: "أداة إدارة مشاريع بسيطة وقوية تساعد الفرق على التعاون بكفاءة وإنجاز المهام في الوقت المحدد.",
                icon: "⚡",
                features: [
                  "إدارة المهام والمشاريع",
                  "تعاون الفريق في الوقت الفعلي",
                  "تتبع التقدم والمواعيد",
                  "إدارة الملفات والمستندات",
                  "تقارير الإنتاجية",
                  "تكاملات مع الأدوات الشائعة"
                ],
                link: "/programs/ease",
                color: "bg-orange-500"
              },
              {
                name: "Syriana Bot",
                title: "المساعد الذكي للأعمال",
                description: "بوت ذكي مدعوم بالذكاء الاصطناعي يساعدك في أتمتة خدمة العملاء والمبيعات والعمليات التجارية.",
                icon: "🤖",
                features: [
                  "خدمة عملاء ذكية 24/7",
                  "أتمتة المبيعات والتسويق",
                  "دعم متعدد المنصات",
                  "تعلم آلي متقدم",
                  "تكامل مع أنظمة CRM",
                  "تحليلات المحادثات"
                ],
                link: "/programs/syriana-bot",
                color: "bg-indigo-500",
                badge: "جديد"
              }
            ].map((program, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-8 hover:bg-white hover:shadow-xl transition-all duration-300 relative"
              >
                {program.badge && (
                  <div className="absolute top-4 left-4 bg-accent text-white text-xs font-semibold px-3 py-1 rounded-full">
                    {program.badge}
                  </div>
                )}
                
                <div className={`${program.color} w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl mb-6`}>
                  {program.icon}
                </div>
                
                <div className="mb-4">
                  <h3 className="text-2xl font-bold text-primary mb-2">{program.title}</h3>
                  <div className="text-accent font-semibold text-lg mb-4">{program.name}</div>
                </div>
                
                <p className="text-gray-600 mb-6">{program.description}</p>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-700 mb-3">المميزات الرئيسية:</h4>
                  <ul className="space-y-2">
                    {program.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className="w-2 h-2 bg-accent rounded-full ml-3 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex gap-3">
                  <Link
                    href={program.link}
                    className="flex-1 bg-accent hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-semibold text-center transition-colors duration-300"
                  >
                    تفاصيل أكثر
                  </Link>
                  <Link
                    href="/contact"
                    className="flex-1 border-2 border-accent text-accent hover:bg-accent hover:text-white px-6 py-3 rounded-lg font-semibold text-center transition-all duration-300"
                  >
                    اطلب عرض سعر
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Comparison Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              أيهم يناسب أعمالك؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              دليل سريع لمساعدتك في اختيار البرنامج المناسب
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                type: "الأعمال العامة",
                description: "إدارة شاملة للأعمال والمبيعات",
                recommended: "dash",
                icon: "🏢"
              },
              {
                type: "الخدمات والحجوزات",
                description: "عيادات، صالونات، مراكز خدمية",
                recommended: "sybooking",
                icon: "📅"
              },
              {
                type: "التسويق الرقمي",
                description: "إدارة الحملات والروابط",
                recommended: "sylink",
                icon: "📈"
              },
              {
                type: "إدارة المشاريع",
                description: "فرق العمل والمشاريع",
                recommended: "ease",
                icon: "⚡"
              },
              {
                type: "خدمة العملاء",
                description: "أتمتة التفاعل مع العملاء",
                recommended: "Syriana Bot",
                icon: "🤖"
              },
              {
                type: "حلول مخصصة",
                description: "احتياجات خاصة ومعقدة",
                recommended: "تواصل معنا",
                icon: "🎯"
              }
            ].map((guide, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{guide.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-2">{guide.type}</h3>
                <p className="text-gray-600 text-sm mb-4">{guide.description}</p>
                <div className="bg-accent/10 text-accent px-4 py-2 rounded-lg font-semibold text-sm">
                  نوصي بـ: {guide.recommended}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لتجربة برامجنا؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احصل على عرض سعر مخصص أو جرب أي من برامجنا مع فترة تجريبية مجانية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                اطلب عرض سعر مخصص
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع مختص
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default ProgramsPage
