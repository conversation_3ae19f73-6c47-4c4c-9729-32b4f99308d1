'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const PartnersPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🤝</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              شركاؤنا في النجاح
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              نؤمن بقوة الشراكات الاستراتيجية. انضم إلى شبكة شركائنا واستفد من فرص النمو المتبادل، 
              أو اكتشف المنتجات والخدمات التي نوصي بها لعملائنا.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/partners/affiliate"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                كن شريكنا
              </Link>
              <Link 
                href="#partnership-types"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                أنواع الشراكات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Partnership Benefits */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا الشراكة معنا؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نقدم فرص شراكة متنوعة ومربحة مع دعم شامل لضمان نجاحك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "💰",
                title: "عوائد مجزية",
                description: "عمولات تنافسية تصل إلى 30% من قيمة المبيعات"
              },
              {
                icon: "📈",
                title: "نمو مستمر",
                description: "فرص نمو متزايدة مع توسع قاعدة عملائنا"
              },
              {
                icon: "🎯",
                title: "دعم تسويقي",
                description: "مواد تسويقية احترافية وحملات مشتركة"
              },
              {
                icon: "🤝",
                title: "علاقة طويلة المدى",
                description: "شراكات استراتيجية مبنية على الثقة والنمو المتبادل"
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{benefit.title}</h3>
                <p className="text-gray-600 text-sm">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Types */}
      <section id="partnership-types" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              أنواع الشراكات المتاحة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر نوع الشراكة التي تناسب أهدافك وإمكانياتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {[
              {
                title: "شريك تسويق بالعمولة",
                description: "احصل على عمولة مقابل كل عميل تحيله إلينا",
                icon: "🎯",
                features: [
                  "عمولة تصل إلى 25%",
                  "تتبع دقيق للإحالات",
                  "مواد تسويقية جاهزة",
                  "دفعات شهرية منتظمة",
                  "دعم تسويقي مستمر"
                ],
                link: "/partners/affiliate",
                color: "bg-blue-500",
                badge: "الأكثر شيوعاً"
              },
              {
                title: "وكيل إعادة البيع",
                description: "بع منتجاتنا تحت علامتك التجارية",
                icon: "🏪",
                features: [
                  "خصومات تصل إلى 40%",
                  "علامة تجارية مخصصة",
                  "دعم فني شامل",
                  "تدريب متخصص",
                  "حقوق حصرية في منطقتك"
                ],
                link: "/partners/reseller",
                color: "bg-green-500",
                badge: "مربح جداً"
              },
              {
                title: "شريك تقني",
                description: "تعاون في تطوير وتقديم الحلول التقنية",
                icon: "⚙️",
                features: [
                  "مشاركة في الأرباح",
                  "تطوير مشترك للمنتجات",
                  "تبادل الخبرات التقنية",
                  "مشاريع مشتركة",
                  "نمو متبادل"
                ],
                link: "/contact",
                color: "bg-purple-500",
                badge: "للخبراء"
              }
            ].map((partnership, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-8 hover:bg-white hover:shadow-xl transition-all duration-300 relative"
              >
                {partnership.badge && (
                  <div className="absolute top-4 left-4 bg-accent text-white text-xs font-semibold px-3 py-1 rounded-full">
                    {partnership.badge}
                  </div>
                )}
                
                <div className={`${partnership.color} w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl mb-6`}>
                  {partnership.icon}
                </div>
                
                <h3 className="text-2xl font-bold text-primary mb-4">{partnership.title}</h3>
                <p className="text-gray-600 mb-6">{partnership.description}</p>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-700 mb-3">المميزات:</h4>
                  <ul className="space-y-2">
                    {partnership.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className="w-2 h-2 bg-accent rounded-full ml-3 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <Link
                  href={partnership.link}
                  className="block w-full bg-accent hover:bg-accent-600 text-white py-3 rounded-lg font-semibold text-center transition-colors duration-300"
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Current Partners */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              شركاؤنا الحاليون
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نفخر بشراكاتنا مع أفضل الشركات والخبراء في المجال التقني
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            {[
              { name: "TechCorp", logo: "🏢" },
              { name: "DigitalPro", logo: "💻" },
              { name: "CloudHost", logo: "☁️" },
              { name: "WebDesign Co", logo: "🎨" },
              { name: "MobileDev", logo: "📱" },
              { name: "DataAnalytics", logo: "📊" }
            ].map((partner, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-3">{partner.logo}</div>
                <h3 className="text-sm font-semibold text-gray-700">{partner.name}</h3>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Recommended Products */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              منتجات نوصي بها
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              مجموعة مختارة من أفضل المنتجات والخدمات التي نوصي بها لعملائنا
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Cloudflare",
                category: "أمان وأداء المواقع",
                description: "خدمة CDN وحماية مواقع الويب الرائدة عالمياً",
                icon: "🛡️",
                link: "/partners/recommended#cloudflare"
              },
              {
                name: "DigitalOcean",
                category: "الاستضافة السحابية",
                description: "منصة استضافة سحابية موثوقة للمطورين",
                icon: "☁️",
                link: "/partners/recommended#digitalocean"
              },
              {
                name: "Stripe",
                category: "معالجة المدفوعات",
                description: "منصة دفع آمنة وسهلة التكامل",
                icon: "💳",
                link: "/partners/recommended#stripe"
              },
              {
                name: "Mailchimp",
                category: "التسويق عبر البريد",
                description: "أداة تسويق بريد إلكتروني احترافية",
                icon: "📧",
                link: "/partners/recommended#mailchimp"
              },
              {
                name: "Google Workspace",
                category: "أدوات الإنتاجية",
                description: "مجموعة أدوات العمل والتعاون من جوجل",
                icon: "📊",
                link: "/partners/recommended#google-workspace"
              },
              {
                name: "Figma",
                category: "التصميم والنماذج",
                description: "أداة التصميم التعاوني الرائدة",
                icon: "🎨",
                link: "/partners/recommended#figma"
              }
            ].map((product, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{product.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-2">{product.name}</h3>
                <div className="text-accent text-sm font-semibold mb-3">{product.category}</div>
                <p className="text-gray-600 text-sm mb-4">{product.description}</p>
                <Link
                  href={product.link}
                  className="text-accent hover:text-accent-600 font-semibold text-sm transition-colors duration-300"
                >
                  تفاصيل أكثر ←
                </Link>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mt-12"
          >
            <Link
              href="/partners/recommended"
              className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 inline-block"
            >
              شاهد جميع المنتجات الموصى بها
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              قصص نجاح شركائنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اكتشف كيف حقق شركاؤنا نجاحات مميزة من خلال التعاون معنا
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "أحمد محمد",
                role: "شريك تسويق بالعمولة",
                story: "حققت أرباحاً شهرية تزيد عن 15,000 ريال من خلال الشراكة مع Syriana Software",
                earnings: "+15,000 ريال شهرياً",
                avatar: "👨‍💼"
              },
              {
                name: "شركة TechSolutions",
                role: "وكيل إعادة البيع",
                story: "زادت إيراداتنا بنسبة 200% بعد أن أصبحنا وكلاء لمنتجات Syriana Software",
                earnings: "+200% نمو في الإيرادات",
                avatar: "🏢"
              },
              {
                name: "سارة أحمد",
                role: "شريك تقني",
                story: "التعاون التقني معهم ساعدنا في تطوير حلول مبتكرة وتوسيع قاعدة عملائنا",
                earnings: "50+ مشروع مشترك",
                avatar: "👩‍💻"
              }
            ].map((story, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{story.avatar}</div>
                <h3 className="text-lg font-bold text-primary mb-2">{story.name}</h3>
                <div className="text-accent text-sm font-semibold mb-4">{story.role}</div>
                <p className="text-gray-600 text-sm mb-4 italic">"{story.story}"</p>
                <div className="bg-accent/10 text-accent px-4 py-2 rounded-lg font-semibold text-sm">
                  {story.earnings}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لبدء شراكة مربحة؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              انضم إلى شبكة شركائنا الناجحين واستفد من فرص النمو والربح المتاحة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/partners/affiliate"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                ابدأ كشريك تسويق
              </Link>
              <Link
                href="/partners/reseller"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                كن وكيل إعادة بيع
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default PartnersPage
