'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const ResellerPage = () => {
  const [selectedPlan, setSelectedPlan] = useState('silver')

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🏪</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              برنامج إعادة البيع
              <span className="block text-accent">ابني إمبراطوريتك التقنية</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              كن وكيلاً معتمداً لمنتجاتنا وخدماتنا. احصل على خصومات حصرية تصل إلى 40% وبع تحت علامتك التجارية 
              مع دعم فني شامل وحقوق حصرية في منطقتك.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="#plans"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اختر خطتك
              </Link>
              <Link 
                href="#benefits"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف المزايا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا تصبح وكيل إعادة بيع؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              فرصة استثمارية مربحة مع دعم شامل ومنتجات عالية الجودة
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "💰",
                title: "أرباح عالية مضمونة",
                description: "خصومات تصل إلى 40% على جميع المنتجات مع هوامش ربح ممتازة"
              },
              {
                icon: "🏷️",
                title: "علامتك التجارية",
                description: "بع المنتجات تحت علامتك التجارية مع تخصيص كامل للواجهات"
              },
              {
                icon: "🛡️",
                title: "حقوق حصرية",
                description: "احصل على حقوق حصرية للبيع في منطقتك الجغرافية"
              },
              {
                icon: "🎓",
                title: "تدريب شامل",
                description: "برامج تدريبية متخصصة لك ولفريقك على جميع المنتجات"
              },
              {
                icon: "🔧",
                title: "دعم فني كامل",
                description: "فريق دعم فني متخصص لمساعدة عملائك على مدار الساعة"
              },
              {
                icon: "📈",
                title: "مواد تسويقية",
                description: "مواد تسويقية احترافية وحملات إعلانية جاهزة للاستخدام"
              },
              {
                icon: "🚀",
                title: "منتجات مطلوبة",
                description: "منتجات عالية الجودة ومطلوبة بقوة في السوق"
              },
              {
                icon: "📊",
                title: "تقارير مفصلة",
                description: "تقارير مبيعات وأداء مفصلة لمساعدتك في اتخاذ القرارات"
              },
              {
                icon: "🤝",
                title: "شراكة طويلة المدى",
                description: "علاقة شراكة استراتيجية مبنية على النمو المتبادل"
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{benefit.title}</h3>
                <p className="text-gray-600 text-sm">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Reseller Plans */}
      <section id="plans" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الوكالة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب حجم أعمالك وطموحاتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                id: 'silver',
                name: "وكيل فضي",
                discount: "20%",
                investment: "10,000",
                territory: "مدينة واحدة",
                features: [
                  "خصم 20% على جميع المنتجات",
                  "حقوق البيع في مدينة واحدة",
                  "تدريب أساسي للفريق",
                  "دعم فني عبر البريد",
                  "مواد تسويقية أساسية",
                  "تقارير شهرية",
                  "ضمان استرداد لمدة 30 يوم"
                ],
                color: "bg-gray-500"
              },
              {
                id: 'gold',
                name: "وكيل ذهبي",
                discount: "30%",
                investment: "25,000",
                territory: "منطقة كاملة",
                features: [
                  "خصم 30% على جميع المنتجات",
                  "حقوق البيع في منطقة كاملة",
                  "تدريب متقدم ومتخصص",
                  "دعم فني هاتفي مخصص",
                  "مواد تسويقية متقدمة",
                  "تقارير أسبوعية",
                  "ضمان استرداد لمدة 60 يوم",
                  "مدير حساب مخصص"
                ],
                color: "bg-yellow-500",
                popular: true
              },
              {
                id: 'platinum',
                name: "وكيل بلاتيني",
                discount: "40%",
                investment: "50,000",
                territory: "دولة كاملة",
                features: [
                  "خصم 40% على جميع المنتجات",
                  "حقوق البيع في دولة كاملة",
                  "تدريب VIP وورش عمل حصرية",
                  "دعم فني مخصص 24/7",
                  "مواد تسويقية حصرية",
                  "تقارير يومية",
                  "ضمان استرداد لمدة 90 يوم",
                  "فريق دعم مخصص",
                  "مشاركة في تطوير المنتجات"
                ],
                color: "bg-purple-500"
              }
            ].map((plan, index) => (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 relative cursor-pointer border-2 ${
                  selectedPlan === plan.id ? 'border-accent' : 'border-gray-200'
                } ${plan.popular ? 'ring-2 ring-accent transform scale-105' : ''}`}
                onClick={() => setSelectedPlan(plan.id)}
              >
                {plan.popular && (
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full">
                    الأكثر شيوعاً
                  </div>
                )}
                
                <div className={`${plan.color} w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6`}>
                  {plan.discount}
                </div>
                
                <h3 className="text-2xl font-bold text-primary text-center mb-2">{plan.name}</h3>
                <div className="text-center mb-6">
                  <div className="text-3xl font-bold text-accent mb-1">{plan.investment} ريال</div>
                  <div className="text-gray-500 text-sm">استثمار أولي</div>
                  <div className="text-gray-600 text-sm mt-2">{plan.territory}</div>
                </div>
                
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start text-sm">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    selectedPlan === plan.id
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Portfolio */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              محفظة المنتجات
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              منتجات متنوعة وعالية الجودة لتلبية احتياجات جميع العملاء
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "dash",
                title: "لوحة التحكم الشاملة",
                price: "1,999 ريال",
                margin: "40%",
                description: "منصة إدارة متكاملة للأعمال",
                icon: "📊"
              },
              {
                name: "sybooking",
                title: "نظام الحجوزات الذكي",
                price: "1,499 ريال",
                margin: "35%",
                description: "نظام حجوزات متطور للخدمات",
                icon: "📅"
              },
              {
                name: "sylink",
                title: "إدارة الروابط الذكية",
                price: "799 ريال",
                margin: "45%",
                description: "منصة إدارة الروابط المختصرة",
                icon: "🔗"
              },
              {
                name: "ease",
                title: "إدارة المشاريع السهلة",
                price: "1,299 ريال",
                margin: "38%",
                description: "أداة إدارة المشاريع والفرق",
                icon: "⚡"
              },
              {
                name: "خدمات التطوير",
                title: "خدمات التطوير المخصص",
                price: "حسب المشروع",
                margin: "25-35%",
                description: "تطوير مواقع وتطبيقات مخصصة",
                icon: "💻"
              },
              {
                name: "خدمات الاستضافة",
                title: "حلول الاستضافة",
                price: "199 ريال/شهر",
                margin: "30%",
                description: "استضافة سحابية عالية الأداء",
                icon: "☁️"
              }
            ].map((product, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{product.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-2">{product.title}</h3>
                <div className="text-accent font-semibold mb-2">{product.name}</div>
                <p className="text-gray-600 text-sm mb-4">{product.description}</p>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="text-lg font-bold text-gray-800">{product.price}</div>
                    <div className="text-green-600 text-sm font-semibold">هامش ربح: {product.margin}</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              قصص نجاح وكلائنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اكتشف كيف حقق وكلاؤنا نجاحات مميزة
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "شركة التقنية المتقدمة",
                location: "الرياض، السعودية",
                plan: "وكيل ذهبي",
                revenue: "150,000 ريال/شهر",
                story: "بدأنا كوكيل ذهبي منذ سنة واحدة، والآن نحقق إيرادات شهرية تزيد عن 150,000 ريال مع نمو مستمر.",
                avatar: "🏢"
              },
              {
                name: "محمد أحمد التقني",
                location: "دبي، الإمارات",
                plan: "وكيل فضي",
                revenue: "80,000 ريال/شهر",
                story: "الشراكة مع Syriana Software غيرت مسار أعمالي. الدعم الفني والمنتجات عالية الجودة ساعدتني كثيراً.",
                avatar: "👨‍💼"
              },
              {
                name: "شركة الحلول الذكية",
                location: "القاهرة، مصر",
                plan: "وكيل بلاتيني",
                revenue: "300,000 ريال/شهر",
                story: "كوكيل بلاتيني، نستفيد من الخصومات العالية والدعم المتميز. فريقهم يعاملنا كشركاء حقيقيين.",
                avatar: "🏭"
              }
            ].map((story, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{story.avatar}</div>
                <h3 className="text-lg font-bold text-primary mb-2">{story.name}</h3>
                <div className="text-gray-600 text-sm mb-2">{story.location}</div>
                <div className="text-accent text-sm font-semibold mb-4">{story.plan}</div>
                <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold mb-4 inline-block">
                  {story.revenue}
                </div>
                <p className="text-gray-600 text-sm italic">"{story.story}"</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Requirements */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              متطلبات الوكالة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              المتطلبات الأساسية للانضمام لبرنامج الوكالة
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8"
            >
              <h3 className="text-xl font-bold text-primary mb-6">المتطلبات العامة</h3>
              <ul className="space-y-3">
                {[
                  "شركة مسجلة أو مؤسسة فردية",
                  "خبرة في مجال التقنية أو المبيعات",
                  "فريق مبيعات ودعم فني",
                  "خطة عمل واضحة للسوق المستهدف",
                  "القدرة على الاستثمار الأولي",
                  "التزام بمعايير الجودة والخدمة"
                ].map((req, index) => (
                  <li key={index} className="flex items-center text-gray-600">
                    <div className="w-2 h-2 bg-accent rounded-full ml-3 flex-shrink-0"></div>
                    {req}
                  </li>
                ))}
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8"
            >
              <h3 className="text-xl font-bold text-primary mb-6">المستندات المطلوبة</h3>
              <ul className="space-y-3">
                {[
                  "السجل التجاري أو رخصة المؤسسة",
                  "البيانات المالية للسنة الماضية",
                  "خطة العمل والتسويق",
                  "السيرة الذاتية للفريق الإداري",
                  "خطابات توصية من عملاء سابقين",
                  "إثبات القدرة المالية"
                ].map((doc, index) => (
                  <li key={index} className="flex items-center text-gray-600">
                    <div className="w-2 h-2 bg-accent rounded-full ml-3 flex-shrink-0"></div>
                    {doc}
                  </li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لبناء إمبراطوريتك التقنية؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              انضم إلى شبكة وكلائنا الناجحين واحصل على فرصة استثمارية مربحة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                تقدم للوكالة
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع مختص
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default ResellerPage
