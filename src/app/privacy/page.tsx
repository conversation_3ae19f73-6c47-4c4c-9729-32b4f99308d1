'use client'

import { motion } from 'framer-motion'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const PrivacyPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🛡️</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              سياسة الخصوصية
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              نحن في Syriana Software نلتزم بحماية خصوصيتك وأمان بياناتك الشخصية. 
              تعرف على كيفية جمعنا واستخدامنا وحماية معلوماتك.
            </p>
            <div className="text-gray-300 text-sm">
              آخر تحديث: 19 يوليو 2025
            </div>
          </motion.div>
        </div>
      </section>

      {/* Privacy Policy Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            
            {/* Introduction */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">مقدمة</h2>
              <div className="prose prose-lg text-gray-600 space-y-4">
                <p>
                  تحكم هذه السياسة كيفية جمع واستخدام وحماية المعلومات الشخصية التي تقدمها لنا عند استخدام موقعنا الإلكتروني 
                  أو خدماتنا. نحن ملتزمون بضمان حماية خصوصيتك وأمان بياناتك.
                </p>
                <p>
                  من خلال استخدام موقعنا أو خدماتنا، فإنك توافق على جمع واستخدام المعلومات وفقاً لهذه السياسة.
                </p>
              </div>
            </motion.div>

            {/* Information We Collect */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">المعلومات التي نجمعها</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">المعلومات الشخصية</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      الاسم الكامل
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      عنوان البريد الإلكتروني
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      رقم الهاتف
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      عنوان الشركة أو المؤسسة
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      المعلومات المالية (عند الضرورة للمعاملات)
                    </li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">المعلومات التقنية</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      عنوان IP الخاص بك
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      نوع المتصفح ونظام التشغيل
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      الصفحات التي تزورها على موقعنا
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      الوقت والتاريخ لزياراتك
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* How We Use Information */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">كيف نستخدم معلوماتك</h2>
              <div className="space-y-4 text-gray-600">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                  <span>تقديم وتحسين خدماتنا ومنتجاتنا</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                  <span>التواصل معك بخصوص طلباتك واستفساراتك</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                  <span>معالجة المدفوعات والمعاملات المالية</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                  <span>إرسال التحديثات والعروض التسويقية (بموافقتك)</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                  <span>تحليل استخدام الموقع لتحسين تجربة المستخدم</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                  <span>الامتثال للمتطلبات القانونية والتنظيمية</span>
                </div>
              </div>
            </motion.div>

            {/* Data Protection */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">حماية البيانات</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  نتخذ إجراءات أمنية صارمة لحماية معلوماتك الشخصية من الوصول غير المصرح به أو التغيير أو الكشف أو التدمير:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-semibold text-gray-800 mb-3">🔐 التشفير</h3>
                    <p className="text-sm">جميع البيانات الحساسة مشفرة باستخدام أحدث معايير التشفير</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-semibold text-gray-800 mb-3">🛡️ الحماية الفيزيائية</h3>
                    <p className="text-sm">خوادمنا محمية في مراكز بيانات آمنة ومراقبة</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-semibold text-gray-800 mb-3">👥 التحكم في الوصول</h3>
                    <p className="text-sm">وصول محدود للموظفين المصرح لهم فقط</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-semibold text-gray-800 mb-3">🔄 النسخ الاحتياطية</h3>
                    <p className="text-sm">نسخ احتياطية منتظمة وآمنة لجميع البيانات</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Cookies */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">ملفات تعريف الارتباط (Cookies)</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  نستخدم ملفات تعريف الارتباط لتحسين تجربتك على موقعنا. هذه الملفات الصغيرة تساعدنا في:
                </p>
                <div className="space-y-2">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span>تذكر تفضيلاتك وإعداداتك</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span>تحليل كيفية استخدام الموقع</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span>تخصيص المحتوى والإعلانات</span>
                  </div>
                </div>
                <p>
                  يمكنك التحكم في ملفات تعريف الارتباط من خلال إعدادات متصفحك.
                </p>
              </div>
            </motion.div>

            {/* Third Party Services */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">الخدمات الخارجية</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  قد نستخدم خدمات طرف ثالث موثوقة لتحسين خدماتنا:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-2">Google Analytics</h3>
                    <p className="text-sm">لتحليل استخدام الموقع وتحسين الأداء</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-2">معالجات الدفع</h3>
                    <p className="text-sm">لمعالجة المدفوعات بشكل آمن</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-2">خدمات البريد الإلكتروني</h3>
                    <p className="text-sm">لإرسال الرسائل والتحديثات</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-2">خدمات الاستضافة</h3>
                    <p className="text-sm">لاستضافة الموقع والتطبيقات</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Your Rights */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">حقوقك</h2>
              <div className="space-y-4 text-gray-600">
                <p>لديك الحق في:</p>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span><strong>الوصول:</strong> طلب نسخة من بياناتك الشخصية</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span><strong>التصحيح:</strong> طلب تصحيح أي معلومات غير دقيقة</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span><strong>الحذف:</strong> طلب حذف بياناتك الشخصية</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span><strong>التقييد:</strong> طلب تقييد معالجة بياناتك</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span><strong>النقل:</strong> طلب نقل بياناتك إلى خدمة أخرى</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span><strong>الاعتراض:</strong> الاعتراض على معالجة بياناتك لأغراض تسويقية</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Data Retention */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">الاحتفاظ بالبيانات</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  نحتفظ بمعلوماتك الشخصية فقط للمدة اللازمة لتحقيق الأغراض المحددة في هذه السياسة:
                </p>
                <div className="bg-gray-50 rounded-lg p-6">
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span><strong>بيانات العملاء:</strong> طوال فترة العلاقة التجارية + 7 سنوات للأرشفة</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span><strong>بيانات التسويق:</strong> حتى سحب الموافقة أو 3 سنوات من آخر تفاعل</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span><strong>البيانات التقنية:</strong> 2 سنة لأغراض التحليل والتحسين</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">تواصل معنا</h2>
              <div className="bg-gray-50 rounded-lg p-6">
                <p className="text-gray-600 mb-4">
                  إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه أو ترغب في ممارسة حقوقك، يرجى التواصل معنا:
                </p>
                <div className="space-y-2 text-gray-600">
                  <div className="flex items-center">
                    <span className="font-semibold ml-2">البريد الإلكتروني:</span>
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-semibold ml-2">الهاتف:</span>
                    <span>+20 106 617 0179</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-semibold ml-2">العنوان:</span>
                    <span>مصر، القاهرة</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Updates */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">تحديثات السياسة</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  قد نقوم بتحديث سياسة الخصوصية هذه من وقت لآخر. سنقوم بإشعارك بأي تغييرات جوهرية عن طريق:
                </p>
                <div className="space-y-2">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span>نشر السياسة المحدثة على موقعنا</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span>إرسال إشعار عبر البريد الإلكتروني</span>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                    <span>إشعار على الموقع أو التطبيق</span>
                  </div>
                </div>
                <p>
                  استمرار استخدامك لخدماتنا بعد التحديث يعني موافقتك على السياسة المحدثة.
                </p>
              </div>
            </motion.div>

          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default PrivacyPage
