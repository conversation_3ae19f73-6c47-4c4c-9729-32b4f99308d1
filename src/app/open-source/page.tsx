'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const OpenSourcePage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'جميع الأدوات', icon: '🔧' },
    { id: 'web', name: 'تطوير الويب', icon: '🌐' },
    { id: 'mobile', name: 'تطبيقات الموبايل', icon: '📱' },
    { id: 'ai', name: 'الذكاء الاصطناعي', icon: '🤖' },
    { id: 'tools', name: 'أدوات مساعدة', icon: '⚙️' },
    { id: 'templates', name: 'قوالب جاهزة', icon: '📋' }
  ]

  const projects = [
    {
      id: 1,
      name: "SyUI Components",
      category: "web",
      description: "مكتبة مكونات React جاهزة مع دعم RTL والعربية",
      technologies: ["React", "TypeScript", "Tailwind CSS", "Storybook"],
      features: ["دعم RTL", "مكونات جاهزة", "تخصيص سهل", "توثيق شامل"],
      github: "https://github.com/syriana-software/syui",
      demo: "https://syui.syriana.software",
      downloads: "2.5K",
      stars: "156",
      license: "MIT",
      status: "نشط",
      icon: "⚛️"
    },
    {
      id: 2,
      name: "Arabic Form Validator",
      category: "web",
      description: "مكتبة JavaScript للتحقق من صحة النماذج باللغة العربية",
      technologies: ["JavaScript", "TypeScript", "Jest"],
      features: ["تحقق من الأسماء العربية", "تحقق من أرقام الهوية", "رسائل خطأ عربية", "سهل الاستخدام"],
      github: "https://github.com/syriana-software/arabic-validator",
      demo: "https://validator.syriana.software",
      downloads: "1.8K",
      stars: "89",
      license: "MIT",
      status: "نشط",
      icon: "✅"
    },
    {
      id: 3,
      name: "React Native Arabic Kit",
      category: "mobile",
      description: "مجموعة أدوات لتطوير تطبيقات React Native باللغة العربية",
      technologies: ["React Native", "TypeScript", "Expo"],
      features: ["مكونات عربية", "تخطيط RTL", "خطوط عربية", "أمثلة شاملة"],
      github: "https://github.com/syriana-software/rn-arabic-kit",
      demo: "https://expo.dev/@syriana/arabic-kit",
      downloads: "950",
      stars: "67",
      license: "MIT",
      status: "نشط",
      icon: "📱"
    },
    {
      id: 4,
      name: "AI Arabic Chatbot",
      category: "ai",
      description: "بوت ذكي مفتوح المصدر يدعم اللغة العربية",
      technologies: ["Python", "TensorFlow", "FastAPI", "Docker"],
      features: ["فهم اللغة العربية", "ردود ذكية", "تدريب مخصص", "API جاهز"],
      github: "https://github.com/syriana-software/arabic-chatbot",
      demo: "https://chatbot.syriana.software",
      downloads: "1.2K",
      stars: "134",
      license: "Apache 2.0",
      status: "نشط",
      icon: "🤖"
    },
    {
      id: 5,
      name: "Arabic Text Analyzer",
      category: "ai",
      description: "أداة تحليل النصوص العربية باستخدام الذكاء الاصطناعي",
      technologies: ["Python", "NLTK", "spaCy", "Flask"],
      features: ["تحليل المشاعر", "استخراج الكلمات المفتاحية", "تصنيف النصوص", "إحصائيات مفصلة"],
      github: "https://github.com/syriana-software/arabic-analyzer",
      demo: "https://analyzer.syriana.software",
      downloads: "800",
      stars: "92",
      license: "MIT",
      status: "نشط",
      icon: "📊"
    },
    {
      id: 6,
      name: "SyTools CLI",
      category: "tools",
      description: "أدوات سطر الأوامر لتسريع التطوير",
      technologies: ["Node.js", "Commander.js", "Inquirer"],
      features: ["إنشاء مشاريع سريع", "قوالب جاهزة", "أتمتة المهام", "تكوين سهل"],
      github: "https://github.com/syriana-software/sytools",
      demo: "https://www.npmjs.com/package/sytools",
      downloads: "3.1K",
      stars: "78",
      license: "MIT",
      status: "نشط",
      icon: "🛠️"
    },
    {
      id: 7,
      name: "Arabic Dashboard Template",
      category: "templates",
      description: "قالب لوحة تحكم احترافية باللغة العربية",
      technologies: ["Next.js", "Tailwind CSS", "Chart.js", "TypeScript"],
      features: ["تصميم عربي", "مخططات تفاعلية", "جداول متقدمة", "نماذج جاهزة"],
      github: "https://github.com/syriana-software/arabic-dashboard",
      demo: "https://dashboard-template.syriana.software",
      downloads: "1.5K",
      stars: "203",
      license: "MIT",
      status: "نشط",
      icon: "📊"
    },
    {
      id: 8,
      name: "E-commerce Template",
      category: "templates",
      description: "قالب متجر إلكتروني كامل مفتوح المصدر",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      features: ["متجر كامل", "نظام دفع", "إدارة المنتجات", "لوحة إدارة"],
      github: "https://github.com/syriana-software/ecommerce-template",
      demo: "https://ecommerce-demo.syriana.software",
      downloads: "2.8K",
      stars: "312",
      license: "MIT",
      status: "نشط",
      icon: "🛒"
    }
  ]

  const filteredProjects = selectedCategory === 'all' 
    ? projects 
    : projects.filter(project => project.category === selectedCategory)

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🔓</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              الحلول مفتوحة المصدر
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              مجموعة من الأدوات والمكتبات مفتوحة المصدر التي طورناها لخدمة المجتمع التقني العربي. 
              استخدمها مجاناً وساهم في تطويرها.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="https://github.com/syriana-software"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تصفح GitHub
              </Link>
              <Link 
                href="#projects"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                شاهد المشاريع
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              إحصائيات مشاريعنا مفتوحة المصدر
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              أرقام تعكس تأثيرنا في المجتمع التقني
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                number: "25+",
                label: "مشروع مفتوح المصدر",
                description: "أدوات ومكتبات متنوعة"
              },
              {
                number: "15K+",
                label: "تحميل شهرياً",
                description: "استخدام واسع من المطورين"
              },
              {
                number: "1.2K+",
                label: "نجمة على GitHub",
                description: "تقدير من المجتمع التقني"
              },
              {
                number: "50+",
                label: "مساهم",
                description: "مطورون يساهمون في التطوير"
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg"
              >
                <div className="text-4xl font-bold text-accent mb-2">{stat.number}</div>
                <h3 className="text-lg font-semibold text-primary mb-2">{stat.label}</h3>
                <p className="text-gray-600 text-sm">{stat.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Filter Categories */}
      <section id="projects" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مشاريعنا مفتوحة المصدر
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              اختر الفئة التي تهمك لاستكشاف المشاريع
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-accent text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-xl ml-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift border border-gray-100"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-4xl">{project.icon}</div>
                    <div className="flex items-center gap-2">
                      <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-semibold">
                        {project.status}
                      </span>
                      <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-semibold">
                        {project.license}
                      </span>
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-primary mb-3">{project.name}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{project.description}</p>

                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">التقنيات:</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">المميزات:</h4>
                    <ul className="space-y-1">
                      {project.features.slice(0, 3).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-xs text-gray-600">
                          <div className="w-1.5 h-1.5 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
                    <div className="flex items-center gap-4">
                      <span>⭐ {project.stars}</span>
                      <span>📥 {project.downloads}</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Link
                      href={project.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-gray-900 hover:bg-gray-800 text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-colors duration-300"
                    >
                      GitHub
                    </Link>
                    <Link
                      href={project.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-accent hover:bg-accent-600 text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-colors duration-300"
                    >
                      تجربة مباشرة
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contribution Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              كيف تساهم معنا؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نرحب بمساهماتك في تطوير هذه المشاريع
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "🐛",
                title: "الإبلاغ عن الأخطاء",
                description: "ساعدنا في اكتشاف وإصلاح الأخطاء",
                action: "أبلغ عن خطأ"
              },
              {
                icon: "💡",
                title: "اقتراح ميزات جديدة",
                description: "شاركنا أفكارك لتحسين المشاريع",
                action: "اقترح ميزة"
              },
              {
                icon: "📝",
                title: "تحسين التوثيق",
                description: "ساعد في كتابة وتحسين التوثيق",
                action: "حسن التوثيق"
              },
              {
                icon: "💻",
                title: "المساهمة بالكود",
                description: "ساهم بكتابة الكود وإصلاح المشاكل",
                action: "ساهم بالكود"
              }
            ].map((way, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{way.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{way.title}</h3>
                <p className="text-gray-600 mb-4 text-sm">{way.description}</p>
                <Link
                  href="https://github.com/syriana-software"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-accent hover:bg-accent-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-colors duration-300"
                >
                  {way.action}
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              انضم إلى مجتمعنا التقني
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              تابع مشاريعنا الجديدة وساهم في بناء مستقبل التقنية العربية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="https://github.com/syriana-software"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                تابعنا على GitHub
              </Link>
              <Link
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تواصل معنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default OpenSourcePage
