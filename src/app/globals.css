@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 51, 51, 51;
  --background-start-rgb: 248, 249, 250;
  --background-end-rgb: 255, 255, 255;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Tajawal', sans-serif;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #00BFA6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #009985;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .gradient-primary {
    background: linear-gradient(135deg, #0A2540 0%, #005269 100%);
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, #00BFA6 0%, #009985 100%);
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(0, 191, 166, 0.3);
  }
  
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .hover-lift:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  /* Additional animations */
  .fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  .slide-up {
    animation: slideUp 0.6s ease-out;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
