'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const SaasGrowthPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🚀</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              برنامج نمو SaaS
              <span className="block text-accent">منصة النمو الذكية</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              منصة متكاملة تساعد شركات SaaS على تسريع نموها من خلال أدوات تحليل متقدمة، 
              استراتيجيات تسويق ذكية، وحلول تحسين معدلات التحويل.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                ابدأ رحلة النمو
              </Link>
              <Link 
                href="#features"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف المميزات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-red-50 rounded-2xl p-8 border-l-4 border-red-500"
            >
              <h2 className="text-2xl font-bold text-red-700 mb-6">التحديات التي تواجه شركات SaaS</h2>
              <div className="space-y-4">
                {[
                  "صعوبة في اكتساب عملاء جدد بتكلفة معقولة",
                  "معدلات تحويل منخفضة من التجربة المجانية للاشتراك المدفوع",
                  "ارتفاع معدل إلغاء الاشتراكات (Churn Rate)",
                  "صعوبة في فهم سلوك المستخدمين وتحليل البيانات",
                  "نقص في الاستراتيجيات المناسبة لكل مرحلة نمو",
                  "تحديات في التوسع والنمو المستدام"
                ].map((challenge, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-red-500 text-xl ml-3 mt-1">❌</div>
                    <span className="text-gray-700">{challenge}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-green-50 rounded-2xl p-8 border-l-4 border-green-500"
            >
              <h2 className="text-2xl font-bold text-green-700 mb-6">كيف يحل برنامج نمو SaaS هذه التحديات</h2>
              <div className="space-y-4">
                {[
                  "استراتيجيات اكتساب عملاء محسنة ومدروسة",
                  "تحسين مسار التحويل وزيادة معدلات الاشتراك",
                  "برامج الاحتفاظ بالعملاء وتقليل معدل الإلغاء",
                  "تحليلات متقدمة ورؤى عميقة عن سلوك المستخدمين",
                  "خطط نمو مخصصة لكل مرحلة من مراحل الشركة",
                  "استراتيجيات توسع مدروسة ومستدامة"
                ].map((solution, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-green-500 text-xl ml-3 mt-1">✅</div>
                    <span className="text-gray-700">{solution}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              المميزات الأساسية
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              أدوات شاملة لتسريع نمو شركتك SaaS
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📊",
                title: "تحليلات النمو المتقدمة",
                description: "تتبع مؤشرات الأداء الرئيسية مع تحليلات عميقة",
                features: ["MRR/ARR Tracking", "Customer Lifetime Value", "Churn Analysis", "Cohort Analysis"]
              },
              {
                icon: "🎯",
                title: "تحسين معدلات التحويل",
                description: "أدوات لتحسين كل مرحلة في مسار العميل",
                features: ["A/B Testing", "Funnel Optimization", "Landing Page Analysis", "User Journey Mapping"]
              },
              {
                icon: "👥",
                title: "إدارة العملاء الذكية",
                description: "نظام CRM متخصص لشركات SaaS",
                features: ["Customer Segmentation", "Behavioral Tracking", "Engagement Scoring", "Automated Workflows"]
              },
              {
                icon: "📈",
                title: "استراتيجيات النمو",
                description: "خطط نمو مخصصة حسب مرحلة شركتك",
                features: ["Growth Playbooks", "Market Analysis", "Competitive Intelligence", "Expansion Strategies"]
              },
              {
                icon: "🔄",
                title: "الاحتفاظ بالعملاء",
                description: "برامج متقدمة لتقليل معدل إلغاء الاشتراكات",
                features: ["Churn Prediction", "Retention Campaigns", "Customer Success Tools", "Loyalty Programs"]
              },
              {
                icon: "💰",
                title: "تحسين الإيرادات",
                description: "استراتيجيات لزيادة الإيرادات من العملاء الحاليين",
                features: ["Upselling Automation", "Pricing Optimization", "Revenue Forecasting", "Monetization Strategies"]
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Growth Stages */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مراحل النمو المختلفة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              استراتيجيات مخصصة لكل مرحلة من مراحل نمو شركتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                stage: "البداية",
                revenue: "$0 - $10K MRR",
                focus: "Product-Market Fit",
                strategies: ["تطوير المنتج", "فهم السوق", "أول 100 عميل", "تحسين UX"],
                color: "bg-blue-500"
              },
              {
                stage: "النمو المبكر",
                revenue: "$10K - $100K MRR",
                focus: "Customer Acquisition",
                strategies: ["تحسين التسويق", "بناء الفريق", "تطوير المبيعات", "تحسين المنتج"],
                color: "bg-green-500"
              },
              {
                stage: "النمو السريع",
                revenue: "$100K - $1M MRR",
                focus: "Scaling Operations",
                strategies: ["أتمتة العمليات", "توسيع الفريق", "دخول أسواق جديدة", "تحسين الاحتفاظ"],
                color: "bg-orange-500"
              },
              {
                stage: "النضج",
                revenue: "$1M+ MRR",
                focus: "Market Leadership",
                strategies: ["الابتكار المستمر", "التوسع الدولي", "الاستحواذات", "القيادة السوقية"],
                color: "bg-purple-500"
              }
            ].map((stage, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className={`w-full h-2 ${stage.color} rounded-full mb-4`}></div>
                <h3 className="text-xl font-bold text-primary mb-2">{stage.stage}</h3>
                <p className="text-accent font-semibold mb-2">{stage.revenue}</p>
                <p className="text-gray-600 font-medium mb-4">{stage.focus}</p>
                <ul className="space-y-2">
                  {stage.strategies.map((strategy, strategyIndex) => (
                    <li key={strategyIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {strategy}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              نتائج حقيقية لعملائنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              إحصائيات مذهلة حققتها الشركات التي استخدمت برنامج نمو SaaS
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                metric: "300%",
                label: "زيادة في معدل التحويل",
                description: "متوسط تحسن معدل التحويل من التجربة للاشتراك المدفوع"
              },
              {
                metric: "65%",
                label: "تقليل معدل الإلغاء",
                description: "انخفاض في معدل إلغاء الاشتراكات خلال 6 أشهر"
              },
              {
                metric: "250%",
                label: "نمو في الإيرادات",
                description: "متوسط نمو الإيرادات الشهرية المتكررة (MRR)"
              },
              {
                metric: "40%",
                label: "تقليل تكلفة الاكتساب",
                description: "انخفاض في تكلفة اكتساب العميل (CAC)"
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-gradient-to-br from-primary to-primary-600 text-white rounded-2xl p-6"
              >
                <div className="text-4xl font-bold text-accent mb-2">{stat.metric}</div>
                <h3 className="text-lg font-semibold mb-3">{stat.label}</h3>
                <p className="text-white/90 text-sm">{stat.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الاشتراك
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب مرحلة نمو شركتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Startup",
                price: "999",
                period: "شهرياً",
                description: "للشركات الناشئة حتى $50K MRR",
                features: [
                  "تحليلات أساسية",
                  "تتبع المؤشرات الرئيسية",
                  "تقارير شهرية",
                  "دعم فني عبر البريد",
                  "حتى 1000 عميل",
                  "استشارة شهرية"
                ]
              },
              {
                name: "Growth",
                price: "2,999",
                period: "شهرياً",
                description: "للشركات في مرحلة النمو حتى $500K MRR",
                features: [
                  "كل ميزات Startup",
                  "تحليلات متقدمة",
                  "A/B Testing",
                  "تحسين معدلات التحويل",
                  "حتى 10,000 عميل",
                  "استشارة أسبوعية",
                  "خطة نمو مخصصة"
                ],
                popular: true
              },
              {
                name: "Scale",
                price: "7,999",
                period: "شهرياً",
                description: "للشركات الكبيرة +$500K MRR",
                features: [
                  "كل ميزات Growth",
                  "تحليلات مؤسسية",
                  "استراتيجيات توسع",
                  "مدير حساب مخصص",
                  "عملاء غير محدودين",
                  "دعم أولوية 24/7",
                  "تدريب فريق العمل",
                  "تكاملات مخصصة"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-2xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {plan.price} <span className="text-lg text-gray-600">ريال</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لتسريع نمو شركتك SaaS؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              انضم إلى مئات الشركات التي حققت نمواً استثنائياً مع برنامج نمو SaaS
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احجز استشارة مجانية
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع خبير نمو
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default SaasGrowthPage
