'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const EasePage = () => {
  const t = useTranslations('programs.ease')
  const locale = useLocale()
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">⚡</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              ease
              <span className="block text-accent">{t('hero.subtitle')}</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                ابدأ مجاناً
              </Link>
              <Link 
                href="#features"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف المميزات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-red-50 rounded-2xl p-8 border-l-4 border-red-500"
            >
              <h2 className="text-2xl font-bold text-red-700 mb-6">تحديات إدارة المشاريع التقليدية</h2>
              <div className="space-y-4">
                {[
                  "أدوات معقدة تحتاج تدريب مكثف",
                  "صعوبة في التعاون بين أعضاء الفريق",
                  "فقدان المهام والمواعيد النهائية",
                  "عدم وضوح حالة المشروع والتقدم",
                  "تشتت المعلومات في أماكن متعددة",
                  "صعوبة في تتبع الوقت والموارد"
                ].map((challenge, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-red-500 text-xl ml-3 mt-1">❌</div>
                    <span className="text-gray-700">{challenge}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-green-50 rounded-2xl p-8 border-l-4 border-green-500"
            >
              <h2 className="text-2xl font-bold text-green-700 mb-6">كيف يحل ease هذه التحديات</h2>
              <div className="space-y-4">
                {[
                  "واجهة بسيطة وسهلة الاستخدام للجميع",
                  "تعاون سلس ومشاركة فورية للمعلومات",
                  "تذكيرات ذكية وتنبيهات تلقائية",
                  "رؤية واضحة لحالة المشروع في الوقت الفعلي",
                  "مركزية جميع المعلومات في مكان واحد",
                  "تتبع دقيق للوقت والموارد والتكاليف"
                ].map((solution, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-green-500 text-xl ml-3 mt-1">✅</div>
                    <span className="text-gray-700">{solution}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مميزات ease الأساسية
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              كل ما تحتاجه لإدارة مشاريعك بنجاح
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📋",
                title: "إدارة المهام",
                description: "تنظيم وتتبع جميع مهام المشروع بسهولة",
                features: ["قوائم المهام", "الأولويات", "المواعيد النهائية", "التبعيات"]
              },
              {
                icon: "👥",
                title: "تعاون الفريق",
                description: "تعاون سلس بين جميع أعضاء الفريق",
                features: ["التعليقات", "المشاركة", "الإشعارات", "المناقشات"]
              },
              {
                icon: "📊",
                title: "تتبع التقدم",
                description: "مراقبة تقدم المشروع في الوقت الفعلي",
                features: ["لوحات المعلومات", "التقارير", "الرسوم البيانية", "المؤشرات"]
              },
              {
                icon: "⏰",
                title: "إدارة الوقت",
                description: "تتبع الوقت المستغرق في كل مهمة",
                features: ["مؤقت الوقت", "تقارير الوقت", "تحليل الإنتاجية", "الفواتير"]
              },
              {
                icon: "📅",
                title: "التقويم والجدولة",
                description: "تنظيم المواعيد والأحداث المهمة",
                features: ["التقويم المشترك", "الأحداث", "التذكيرات", "المواعيد النهائية"]
              },
              {
                icon: "📁",
                title: "إدارة الملفات",
                description: "تنظيم ومشاركة ملفات المشروع",
                features: ["رفع الملفات", "المشاركة", "التحكم في الإصدارات", "التخزين السحابي"]
              },
              {
                icon: "💬",
                title: "التواصل المباشر",
                description: "تواصل فوري بين أعضاء الفريق",
                features: ["الدردشة", "الرسائل", "الإشعارات", "المكالمات"]
              },
              {
                icon: "📈",
                title: "التقارير والتحليلات",
                description: "تقارير مفصلة عن أداء المشروع",
                features: ["تقارير مخصصة", "تحليل الأداء", "إحصائيات الفريق", "تصدير البيانات"]
              },
              {
                icon: "🔗",
                title: "التكاملات",
                description: "ربط مع الأدوات والخدمات الأخرى",
                features: ["Google Workspace", "Slack", "Zoom", "GitHub"]
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Project Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              أنواع المشاريع التي يدعمها ease
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              مرونة كاملة لجميع أنواع المشاريع والفرق
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "💻",
                title: "مشاريع التطوير",
                description: "إدارة مشاريع البرمجة والتطوير",
                examples: ["تطوير التطبيقات", "مواقع الويب", "APIs", "اختبار البرمجيات"]
              },
              {
                icon: "🎨",
                title: "المشاريع الإبداعية",
                description: "إدارة المشاريع التصميمية والإبداعية",
                examples: ["التصميم الجرافيكي", "إنتاج الفيديو", "التصوير", "الحملات الإعلانية"]
              },
              {
                icon: "📊",
                title: "المشاريع التسويقية",
                description: "تنظيم الحملات والاستراتيجيات التسويقية",
                examples: ["حملات التسويق", "إدارة المحتوى", "وسائل التواصل", "تحليل السوق"]
              },
              {
                icon: "🏗️",
                title: "المشاريع الهندسية",
                description: "إدارة المشاريع الهندسية والإنشائية",
                examples: ["البناء والتشييد", "التصميم المعماري", "الهندسة المدنية", "إدارة المقاولات"]
              },
              {
                icon: "📚",
                title: "المشاريع التعليمية",
                description: "تنظيم المشاريع الأكاديمية والتدريبية",
                examples: ["المناهج الدراسية", "الدورات التدريبية", "البحث العلمي", "المؤتمرات"]
              },
              {
                icon: "🎪",
                title: "إدارة الفعاليات",
                description: "تنظيم الفعاليات والمؤتمرات",
                examples: ["المؤتمرات", "المعارض", "الحفلات", "ورش العمل"]
              },
              {
                icon: "🏢",
                title: "المشاريع المؤسسية",
                description: "إدارة المشاريع الداخلية للشركات",
                examples: ["التحول الرقمي", "تطوير العمليات", "إدارة التغيير", "التدريب"]
              },
              {
                icon: "🔬",
                title: "مشاريع البحث والتطوير",
                description: "إدارة مشاريع الابتكار والبحث",
                examples: ["البحث العلمي", "تطوير المنتجات", "الابتكار", "التجارب"]
              }
            ].map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{type.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{type.title}</h3>
                <p className="text-gray-600 mb-4 text-sm">{type.description}</p>
                <ul className="space-y-1">
                  {type.examples.map((example, exampleIndex) => (
                    <li key={exampleIndex} className="text-xs text-gray-500">
                      {example}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا تختار ease؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              فوائد حقيقية تحدث فرقاً في إنتاجية فريقك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "⚡",
                title: "سهولة الاستخدام",
                description: "واجهة بديهية لا تحتاج تدريب معقد",
                stat: "5 دقائق للبدء"
              },
              {
                icon: "📈",
                title: "زيادة الإنتاجية",
                description: "تحسين كفاءة الفريق وسرعة الإنجاز",
                stat: "40% زيادة في الإنتاجية"
              },
              {
                icon: "💰",
                title: "توفير التكاليف",
                description: "تقليل الوقت المهدر والموارد الضائعة",
                stat: "30% توفير في التكاليف"
              },
              {
                icon: "🎯",
                title: "تحسين الجودة",
                description: "ضمان جودة المخرجات والالتزام بالمعايير",
                stat: "95% رضا العملاء"
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-gradient-to-br from-primary to-primary-600 text-white rounded-2xl p-6"
              >
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
                <p className="text-white/90 mb-4 text-sm">{benefit.description}</p>
                <div className="text-accent font-bold text-lg">{benefit.stat}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الاشتراك
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب حجم فريقك ومشاريعك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "الفريق الصغير",
                price: "199",
                period: "شهرياً",
                description: "مثالي للفرق حتى 10 أشخاص",
                features: [
                  "حتى 10 مستخدمين",
                  "مشاريع غير محدودة",
                  "مساحة تخزين 10 جيجا",
                  "دعم فني عبر البريد",
                  "تقارير أساسية",
                  "تطبيق الموبايل"
                ]
              },
              {
                name: "الفريق المتوسط",
                price: "499",
                period: "شهرياً",
                description: "الأنسب للفرق حتى 50 شخص",
                features: [
                  "حتى 50 مستخدم",
                  "كل ميزات الفريق الصغير",
                  "مساحة تخزين 100 جيجا",
                  "تقارير متقدمة",
                  "تكاملات متقدمة",
                  "دعم فني أولوية",
                  "تتبع الوقت المتقدم",
                  "لوحات معلومات مخصصة"
                ],
                popular: true
              },
              {
                name: "المؤسسة",
                price: "1,299",
                period: "شهرياً",
                description: "للمؤسسات الكبيرة والفرق الموزعة",
                features: [
                  "مستخدمين غير محدودين",
                  "كل ميزات الفريق المتوسط",
                  "مساحة تخزين غير محدودة",
                  "مدير حساب مخصص",
                  "تدريب الفريق",
                  "دعم فني 24/7",
                  "تكاملات مخصصة",
                  "أمان متقدم",
                  "نسخ احتياطية يومية"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-2xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {plan.price} <span className="text-lg text-gray-600">ريال</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لجعل إدارة المشاريع أسهل؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              انضم إلى آلاف الفرق التي تستخدم ease لإنجاز مشاريعها بكفاءة أعلى
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                ابدأ تجربتك المجانية
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع خبير
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default EasePage
