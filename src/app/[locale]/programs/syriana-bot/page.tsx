'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const SyrianaBotPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🤖</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              Syriana Bot
              <span className="block text-accent">مساعدك الذكي للأعمال</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              بوت ذكي مدعوم بالذكاء الاصطناعي يساعدك في أتمتة خدمة العملاء، المبيعات، والعمليات التجارية 
              عبر واتساب، تيليجرام، وجميع منصات التواصل الشائعة.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                احصل على بوتك المخصص
              </Link>
              <Link 
                href="#features"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف الإمكانيات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Platforms */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              متاح على جميع المنصات الشائعة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              تواصل مع عملائك أينما كانوا
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                platform: "WhatsApp Business",
                icon: "💬",
                description: "بوت واتساب للأعمال مع ميزات متقدمة",
                features: ["رسائل تلقائية", "كتالوج المنتجات", "الدفع المدمج", "تحليلات مفصلة"]
              },
              {
                platform: "Telegram",
                icon: "✈️",
                description: "بوت تيليجرام قوي ومرن",
                features: ["أوامر مخصصة", "لوحات تفاعلية", "مجموعات وقنوات", "ملفات متعددة"]
              },
              {
                platform: "Facebook Messenger",
                icon: "📘",
                description: "تكامل مع فيسبوك وإنستغرام",
                features: ["رسائل فورية", "تكامل الصفحات", "إعلانات تفاعلية", "تتبع التحويلات"]
              },
              {
                platform: "موقعك الإلكتروني",
                icon: "🌐",
                description: "دردشة مباشرة على موقعك",
                features: ["نافذة دردشة", "دعم متعدد اللغات", "تكامل CRM", "تحليل الزوار"]
              }
            ].map((platform, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-5xl mb-4 text-center">{platform.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">{platform.platform}</h3>
                <p className="text-gray-600 mb-4 text-center text-sm">{platform.description}</p>
                <ul className="space-y-2">
                  {platform.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              إمكانيات Syriana Bot
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              ذكاء اصطناعي متقدم لخدمة أعمالك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🎯",
                title: "خدمة العملاء الذكية",
                description: "ردود تلقائية ذكية على استفسارات العملاء",
                features: ["فهم اللغة الطبيعية", "ردود مخصصة", "تصعيد للبشر", "متعدد اللغات"]
              },
              {
                icon: "🛒",
                title: "مساعد المبيعات",
                description: "زيادة المبيعات من خلال التفاعل الذكي",
                features: ["عرض المنتجات", "معالجة الطلبات", "توصيات ذكية", "متابعة العملاء"]
              },
              {
                icon: "📅",
                title: "إدارة المواعيد",
                description: "حجز وإدارة المواعيد تلقائياً",
                features: ["حجز فوري", "تذكيرات تلقائية", "إعادة الجدولة", "تأكيد المواعيد"]
              },
              {
                icon: "📊",
                title: "جمع البيانات والاستطلاعات",
                description: "جمع معلومات العملاء وآرائهم",
                features: ["استطلاعات تفاعلية", "جمع التقييمات", "تحليل المشاعر", "تقارير مفصلة"]
              },
              {
                icon: "💳",
                title: "معالجة المدفوعات",
                description: "قبول المدفوعات مباشرة عبر البوت",
                features: ["دفع آمن", "فواتير تلقائية", "تتبع المدفوعات", "تأكيد الدفع"]
              },
              {
                icon: "🔔",
                title: "التنبيهات والإشعارات",
                description: "إرسال تنبيهات مخصصة للعملاء",
                features: ["إشعارات مجدولة", "تنبيهات الطوارئ", "تحديثات الطلبات", "عروض خاصة"]
              },
              {
                icon: "📈",
                title: "تحليلات متقدمة",
                description: "رؤى عميقة عن تفاعل العملاء",
                features: ["إحصائيات التفاعل", "تحليل المحادثات", "معدلات التحويل", "تقارير الأداء"]
              },
              {
                icon: "🔗",
                title: "التكاملات",
                description: "ربط مع أنظمتك الحالية",
                features: ["CRM Integration", "E-commerce", "Payment Gateways", "Analytics Tools"]
              },
              {
                icon: "🎨",
                title: "تخصيص كامل",
                description: "بوت مصمم خصيصاً لأعمالك",
                features: ["شخصية مخصصة", "سيناريوهات مخصصة", "تصميم العلامة التجارية", "لغة مخصصة"]
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              حالات الاستخدام
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              كيف يمكن لـ Syriana Bot أن يساعد مختلف أنواع الأعمال
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "🛒",
                title: "المتاجر الإلكترونية",
                description: "أتمتة المبيعات وخدمة العملاء",
                benefits: ["زيادة المبيعات 40%", "تقليل وقت الاستجابة", "دعم 24/7", "تتبع الطلبات"]
              },
              {
                icon: "🏥",
                title: "العيادات والمستشفيات",
                description: "إدارة المواعيد والاستفسارات الطبية",
                benefits: ["حجز المواعيد", "تذكيرات المرضى", "معلومات طبية", "متابعة العلاج"]
              },
              {
                icon: "🏨",
                title: "الفنادق والمطاعم",
                description: "تحسين تجربة الضيوف والحجوزات",
                benefits: ["حجز الغرف", "طلب الطعام", "خدمة الغرف", "تقييم الخدمة"]
              },
              {
                icon: "🎓",
                title: "المؤسسات التعليمية",
                description: "دعم الطلاب والعمليات الأكاديمية",
                benefits: ["معلومات الدورات", "التسجيل", "الدرجات", "الإرشاد الأكاديمي"]
              },
              {
                icon: "🏢",
                title: "الشركات والمكاتب",
                description: "تحسين التواصل الداخلي والخارجي",
                benefits: ["دعم الموظفين", "إدارة الطلبات", "معلومات الشركة", "التدريب"]
              },
              {
                icon: "🚗",
                title: "خدمات النقل",
                description: "تنسيق الرحلات وخدمة العملاء",
                benefits: ["حجز الرحلات", "تتبع المركبات", "دعم السائقين", "تقييم الخدمة"]
              },
              {
                icon: "💰",
                title: "الخدمات المالية",
                description: "دعم العملاء والاستشارات المالية",
                benefits: ["استفسارات الحسابات", "نصائح مالية", "معالجة الطلبات", "الأمان"]
              },
              {
                icon: "🎪",
                title: "الترفيه والفعاليات",
                description: "إدارة الحجوزات والمعلومات",
                benefits: ["حجز التذاكر", "معلومات الفعاليات", "العروض الخاصة", "التذكيرات"]
              }
            ].map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{useCase.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{useCase.title}</h3>
                <p className="text-gray-600 mb-4 text-sm">{useCase.description}</p>
                <ul className="space-y-1">
                  {useCase.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="text-xs text-gray-500">
                      ✓ {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              فوائد استخدام Syriana Bot
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نتائج حقيقية تحدث فرقاً في أعمالك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "⏰",
                title: "توفير الوقت",
                description: "أتمتة 80% من استفسارات العملاء",
                stat: "80% توفير في الوقت"
              },
              {
                icon: "💰",
                title: "زيادة الإيرادات",
                description: "تحسين معدلات التحويل والمبيعات",
                stat: "35% زيادة في المبيعات"
              },
              {
                icon: "😊",
                title: "رضا العملاء",
                description: "استجابة فورية ودعم على مدار الساعة",
                stat: "95% رضا العملاء"
              },
              {
                icon: "📈",
                title: "تحسين الكفاءة",
                description: "تقليل الأخطاء وزيادة الإنتاجية",
                stat: "50% تحسن في الكفاءة"
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-gradient-to-br from-primary to-primary-600 text-white rounded-2xl p-6"
              >
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-xl font-bold mb-3">{benefit.title}</h3>
                <p className="text-white/90 mb-4 text-sm">{benefit.description}</p>
                <div className="text-accent font-bold text-lg">{benefit.stat}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الاشتراك
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب حجم أعمالك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "البوت الأساسي",
                price: "399",
                period: "شهرياً",
                description: "مثالي للشركات الصغيرة",
                features: [
                  "منصة واحدة (واتساب أو تيليجرام)",
                  "حتى 1000 محادثة شهرياً",
                  "ردود تلقائية أساسية",
                  "تقارير أساسية",
                  "دعم فني عبر البريد",
                  "تخصيص أساسي"
                ]
              },
              {
                name: "البوت المتقدم",
                price: "899",
                period: "شهرياً",
                description: "الأنسب للشركات المتوسطة",
                features: [
                  "جميع المنصات",
                  "محادثات غير محدودة",
                  "ذكاء اصطناعي متقدم",
                  "تكاملات مع الأنظمة",
                  "تحليلات متقدمة",
                  "دعم فني أولوية",
                  "تخصيص كامل",
                  "تدريب الفريق"
                ],
                popular: true
              },
              {
                name: "البوت المؤسسي",
                price: "1,999",
                period: "شهرياً",
                description: "للمؤسسات الكبيرة",
                features: [
                  "كل ميزات البوت المتقدم",
                  "بوتات متعددة",
                  "تكاملات مخصصة",
                  "مدير حساب مخصص",
                  "SLA مضمون",
                  "دعم فني 24/7",
                  "تطوير ميزات مخصصة",
                  "تدريب متقدم",
                  "استشارات استراتيجية"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-2xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {plan.price} <span className="text-lg text-gray-600">ريال</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لثورة في خدمة العملاء؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احصل على بوتك المخصص واكتشف كيف يمكن للذكاء الاصطناعي أن يحول أعمالك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احصل على عرض مخصص
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                جرب البوت الآن
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default SyrianaBotPage
