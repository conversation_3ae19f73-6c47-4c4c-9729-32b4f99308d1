'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import PriceDisplay from '@/components/ui/PriceDisplay'

const SylinkPage = () => {
  const t = useTranslations('common')
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🔗</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              sylink
              <span className="block text-accent">منصة إدارة الروابط الذكية</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              أداة قوية لإنشاء وإدارة وتتبع الروابط المختصرة مع تحليلات متقدمة وميزات تسويقية ذكية 
              لتحسين حملاتك الرقمية وزيادة معدلات التحويل.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                ابدأ مجاناً
              </Link>
              <Link 
                href="#features"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف المميزات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Why Use Link Shortener */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا تحتاج لمختصر الروابط؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              الروابط المختصرة ليست مجرد توفير مساحة، بل أداة تسويقية قوية
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📊",
                title: "تتبع دقيق للأداء",
                description: "احصل على إحصائيات مفصلة عن كل نقرة",
                benefits: ["عدد النقرات", "المصادر", "الأجهزة المستخدمة", "المواقع الجغرافية"]
              },
              {
                icon: "🎯",
                title: "تحسين الحملات التسويقية",
                description: "قس فعالية حملاتك بدقة عالية",
                benefits: ["ROI دقيق", "مقارنة الحملات", "تحسين الاستهداف", "تحليل الجمهور"]
              },
              {
                icon: "🔗",
                title: "روابط احترافية ومخصصة",
                description: "اجعل روابطك تعكس هوية علامتك التجارية",
                benefits: ["نطاق مخصص", "أسماء مميزة", "مظهر احترافي", "ثقة أكبر"]
              },
              {
                icon: "📱",
                title: "تجربة مستخدم محسنة",
                description: "روابط قصيرة وسهلة المشاركة",
                benefits: ["سهولة النسخ", "مشاركة سريعة", "تحميل أسرع", "مظهر نظيف"]
              },
              {
                icon: "🛡️",
                title: "أمان وحماية",
                description: "حماية متقدمة ضد الروابط الضارة",
                benefits: ["فحص الأمان", "حماية من البرمجيات الخبيثة", "تشفير الروابط", "مراقبة مستمرة"]
              },
              {
                icon: "⚡",
                title: "سرعة وموثوقية",
                description: "خدمة سريعة ومتاحة على مدار الساعة",
                benefits: ["استجابة فورية", "99.9% وقت تشغيل", "خوادم عالمية", "تحديثات فورية"]
              }
            ].map((reason, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-4xl mb-4">{reason.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{reason.title}</h3>
                <p className="text-gray-600 mb-4">{reason.description}</p>
                <ul className="space-y-2">
                  {reason.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مميزات sylink المتقدمة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              أكثر من مجرد مختصر روابط - منصة تسويقية متكاملة
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🎨",
                title: "روابط مخصصة",
                description: "إنشاء روابط تعكس هوية علامتك التجارية",
                features: ["نطاق مخصص", "أسماء مميزة", "QR Codes", "Branded Links"]
              },
              {
                icon: "📈",
                title: "تحليلات متقدمة",
                description: "إحصائيات شاملة ومفصلة عن أداء روابطك",
                features: ["Real-time Analytics", "Geographic Data", "Device Tracking", "Referrer Analysis"]
              },
              {
                icon: "🎯",
                title: "استهداف ذكي",
                description: "توجيه المستخدمين حسب الموقع والجهاز",
                features: ["Geo-targeting", "Device Targeting", "Language Targeting", "Time-based Routing"]
              },
              {
                icon: "🔄",
                title: "إعادة التوجيه الذكية",
                description: "توجيه المستخدمين للمحتوى المناسب",
                features: ["Smart Redirects", "A/B Testing", "Split Testing", "Dynamic Routing"]
              },
              {
                icon: "📊",
                title: "لوحة تحكم شاملة",
                description: "إدارة جميع روابطك من مكان واحد",
                features: ["Bulk Management", "Team Collaboration", "Campaign Organization", "Advanced Filters"]
              },
              {
                icon: "🔗",
                title: "API متقدم",
                description: "تكامل سهل مع أنظمتك الحالية",
                features: ["RESTful API", "Webhooks", "Bulk Operations", "Real-time Updates"]
              },
              {
                icon: "🛡️",
                title: "أمان وحماية",
                description: "حماية متقدمة لروابطك وبياناتك",
                features: ["Link Expiration", "Password Protection", "Click Limits", "Fraud Detection"]
              },
              {
                icon: "📱",
                title: "تطبيق الموبايل",
                description: "إدارة روابطك من أي مكان",
                features: ["iOS App", "Android App", "Offline Access", "Push Notifications"]
              },
              {
                icon: "🎪",
                title: "صفحات هبوط مخصصة",
                description: "إنشاء صفحات هبوط جذابة لروابطك",
                features: ["Custom Landing Pages", "Lead Capture", "Social Sharing", "Mobile Optimized"]
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              حالات الاستخدام
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              كيف يمكن لـ sylink أن يساعد في مختلف المجالات
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "📢",
                title: "التسويق الرقمي",
                description: "تتبع فعالية الحملات الإعلانية",
                examples: ["حملات Google Ads", "إعلانات فيسبوك", "التسويق عبر البريد", "المؤثرين"]
              },
              {
                icon: "📱",
                title: "وسائل التواصل الاجتماعي",
                description: "مشاركة محتوى جذاب ومتتبع",
                examples: ["منشورات إنستغرام", "تغريدات تويتر", "قصص سناب شات", "فيديوهات تيك توك"]
              },
              {
                icon: "📧",
                title: "التسويق عبر البريد",
                description: "تحسين معدلات النقر في الرسائل",
                examples: ["النشرات الإخبارية", "العروض الترويجية", "رسائل المتابعة", "الدعوات"]
              },
              {
                icon: "🛒",
                title: "التجارة الإلكترونية",
                description: "تتبع مصادر الزيارات والمبيعات",
                examples: ["روابط المنتجات", "كوبونات الخصم", "صفحات الهبوط", "برامج الإحالة"]
              },
              {
                icon: "📊",
                title: "تحليل الأداء",
                description: "قياس ROI للحملات المختلفة",
                examples: ["مقارنة القنوات", "تحليل الجمهور", "تتبع التحويلات", "تحسين الاستهداف"]
              },
              {
                icon: "🎯",
                title: "إدارة الحملات",
                description: "تنظيم وإدارة حملات متعددة",
                examples: ["حملات موسمية", "إطلاق منتجات", "فعاليات خاصة", "شراكات تجارية"]
              },
              {
                icon: "👥",
                title: "فرق العمل",
                description: "تعاون الفرق في إدارة الروابط",
                examples: ["مشاركة الروابط", "صلاحيات متدرجة", "تقارير الفريق", "إدارة المشاريع"]
              },
              {
                icon: "📈",
                title: "نمو الأعمال",
                description: "استراتيجيات نمو مدعومة بالبيانات",
                examples: ["اختبار الأسواق", "تحليل المنافسين", "تحسين التحويل", "توسيع الوصول"]
              }
            ].map((useCase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{useCase.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{useCase.title}</h3>
                <p className="text-gray-600 mb-4 text-sm">{useCase.description}</p>
                <ul className="space-y-1">
                  {useCase.examples.map((example, exampleIndex) => (
                    <li key={exampleIndex} className="text-xs text-gray-500">
                      {example}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الاشتراك
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب احتياجاتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                name: "مجاني",
                price: "0",
                period: "مجاناً",
                description: "للاستخدام الشخصي والتجريبي",
                features: [
                  "100 رابط شهرياً",
                  "إحصائيات أساسية",
                  "روابط sylink.com",
                  "دعم المجتمع"
                ]
              },
              {
                name: "أساسي",
                price: "99",
                period: "شهرياً",
                description: "للشركات الصغيرة",
                features: [
                  "1,000 رابط شهرياً",
                  "تحليلات متقدمة",
                  "نطاق مخصص",
                  "دعم فني عبر البريد",
                  "QR Codes",
                  "تصدير البيانات"
                ]
              },
              {
                name: "احترافي",
                price: "299",
                period: "شهرياً",
                description: "للشركات المتوسطة",
                features: [
                  "10,000 رابط شهرياً",
                  "كل ميزات الأساسي",
                  "A/B Testing",
                  "استهداف جغرافي",
                  "API Access",
                  "تطبيق الموبايل",
                  "دعم فني أولوية"
                ],
                popular: true
              },
              {
                name: "مؤسسي",
                price: "799",
                period: "شهرياً",
                description: "للمؤسسات الكبيرة",
                features: [
                  "روابط غير محدودة",
                  "كل ميزات الاحترافي",
                  "فرق عمل متعددة",
                  "تكاملات مخصصة",
                  "مدير حساب مخصص",
                  "SLA مضمون",
                  "تدريب الفريق",
                  "دعم 24/7"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-2xl font-bold text-accent mb-2">
                  {plan.price === "0" ? (
                    <span className="text-2xl font-bold text-accent">{t('free')}</span>
                  ) : (
                    <PriceDisplay
                      amount={parseInt(plan.price)}
                      originalCurrency="SAR"
                      className="text-2xl font-bold text-accent"
                    />
                  )}
                </div>
                <p className="text-gray-600 mb-6 text-sm">{plan.description}</p>
                <ul className="space-y-2 mb-6">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  {plan.price === "0" ? "ابدأ مجاناً" : "ابدأ الآن"}
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لتحسين حملاتك التسويقية؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              ابدأ مع sylink اليوم واكتشف قوة الروابط الذكية في تحسين أداء أعمالك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                ابدأ مجاناً الآن
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع خبير تسويق
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default SylinkPage
