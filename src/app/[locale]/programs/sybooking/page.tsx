'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const SybookingPage = () => {
  const t = useTranslations('programs.sybooking')
  const locale = useLocale()
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">📅</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              sybooking
              <span className="block text-accent">{t('hero.subtitle')}</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                ابدأ تجربتك المجانية
              </Link>
              <Link 
                href="#features"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف المميزات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Who Can Use It */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('suitableFor.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('suitableFor.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "🏥",
                title: "العيادات الطبية",
                description: "إدارة مواعيد المرضى والملفات الطبية",
                features: ["حجز المواعيد", "الملفات الطبية", "التذكيرات", "إدارة الأطباء"]
              },
              {
                icon: "💇‍♀️",
                title: "صالونات التجميل",
                description: "تنظيم مواعيد العملاء والخدمات",
                features: ["حجز الخدمات", "إدارة المختصين", "باقات الخدمات", "نظام النقاط"]
              },
              {
                icon: "🏋️‍♂️",
                title: "الصالات الرياضية",
                description: "حجز الحصص والمعدات الرياضية",
                features: ["حجز الحصص", "إدارة المدربين", "الاشتراكات", "تتبع التقدم"]
              },
              {
                icon: "🎓",
                title: "المراكز التعليمية",
                description: "تنظيم الدروس والدورات التدريبية",
                features: ["جدولة الدروس", "إدارة المدرسين", "متابعة الطلاب", "الشهادات"]
              },
              {
                icon: "🏨",
                title: "الفنادق والمنتجعات",
                description: "إدارة حجوزات الغرف والخدمات",
                features: ["حجز الغرف", "إدارة الخدمات", "الفواتير", "تقييم العملاء"]
              },
              {
                icon: "🚗",
                title: "مراكز الصيانة",
                description: "تنظيم مواعيد الصيانة والإصلاح",
                features: ["حجز المواعيد", "تتبع الأعمال", "قطع الغيار", "تاريخ الصيانة"]
              },
              {
                icon: "👨‍⚖️",
                title: "المكاتب الاستشارية",
                description: "إدارة مواعيد الاستشارات والاجتماعات",
                features: ["حجز الاستشارات", "إدارة الملفات", "الفواتير", "المتابعة"]
              },
              {
                icon: "🎪",
                title: "مراكز الترفيه",
                description: "حجز الأنشطة والفعاليات الترفيهية",
                features: ["حجز الأنشطة", "إدارة الفعاليات", "التذاكر", "العروض الخاصة"]
              }
            ].map((business, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-4xl mb-4 text-center">{business.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3 text-center">{business.title}</h3>
                <p className="text-gray-600 mb-4 text-center text-sm">{business.description}</p>
                <ul className="space-y-2">
                  {business.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              المميزات الأساسية
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              كل ما تحتاجه لإدارة حجوزاتك بكفاءة عالية
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📱",
                title: "حجز أونلاين 24/7",
                description: "يمكن للعملاء الحجز في أي وقت من خلال الموقع أو التطبيق",
                features: ["واجهة سهلة الاستخدام", "حجز فوري", "تأكيد تلقائي", "دعم متعدد اللغات"]
              },
              {
                icon: "🔔",
                title: "التذكيرات الذكية",
                description: "تذكيرات تلقائية للعملاء والموظفين",
                features: ["رسائل SMS", "إشعارات البريد", "تذكيرات WhatsApp", "تذكيرات مخصصة"]
              },
              {
                icon: "📊",
                title: "لوحة التحكم الشاملة",
                description: "إدارة كاملة لجميع العمليات من مكان واحد",
                features: ["إحصائيات مفصلة", "تقارير مالية", "تحليل الأداء", "إدارة الموظفين"]
              },
              {
                icon: "💳",
                title: "نظام الدفع المتكامل",
                description: "قبول المدفوعات أونلاين وإدارة الفواتير",
                features: ["دفع آمن", "فواتير تلقائية", "تتبع المدفوعات", "تقارير مالية"]
              },
              {
                icon: "👥",
                title: "إدارة العملاء",
                description: "قاعدة بيانات شاملة لجميع العملاء",
                features: ["ملفات العملاء", "تاريخ الحجوزات", "تفضيلات العملاء", "برامج الولاء"]
              },
              {
                icon: "📅",
                title: "جدولة متقدمة",
                description: "إدارة مرنة للمواعيد والموارد",
                features: ["جدولة ذكية", "إدارة الموارد", "تجنب التعارض", "حجوزات متكررة"]
              },
              {
                icon: "📈",
                title: "تقارير وتحليلات",
                description: "رؤى عميقة عن أداء أعمالك",
                features: ["تقارير مفصلة", "تحليل الاتجاهات", "مؤشرات الأداء", "توقعات الإيرادات"]
              },
              {
                icon: "🔗",
                title: "التكاملات",
                description: "ربط سهل مع الأنظمة والخدمات الأخرى",
                features: ["Google Calendar", "Zoom", "WhatsApp", "أنظمة المحاسبة"]
              },
              {
                icon: "📱",
                title: "تطبيق الموبايل",
                description: "تطبيقات مخصصة للعملاء والموظفين",
                features: ["تطبيق العملاء", "تطبيق الموظفين", "إشعارات فورية", "واجهة سهلة"]
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 mb-4">{feature.description}</p>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {item}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              فوائد استخدام sybooking
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              كيف يمكن لـ sybooking أن يحسن أعمالك ويزيد أرباحك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="space-y-8">
                {[
                  {
                    icon: "⏰",
                    title: "توفير الوقت",
                    description: "تقليل الوقت المطلوب لإدارة الحجوزات بنسبة 80%",
                    stat: "80% توفير في الوقت"
                  },
                  {
                    icon: "📈",
                    title: "زيادة الإيرادات",
                    description: "تحسين معدل الحجوزات وتقليل المواعيد الفائتة",
                    stat: "35% زيادة في الإيرادات"
                  },
                  {
                    icon: "😊",
                    title: "رضا العملاء",
                    description: "تحسين تجربة العملاء وسهولة الحجز",
                    stat: "95% رضا العملاء"
                  },
                  {
                    icon: "📊",
                    title: "اتخاذ قرارات أذكى",
                    description: "بيانات دقيقة وتقارير مفصلة لتحسين الأداء",
                    stat: "100% دقة البيانات"
                  }
                ].map((benefit, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-3xl ml-4 flex-shrink-0">{benefit.icon}</div>
                    <div>
                      <h3 className="text-xl font-bold text-primary mb-2">{benefit.title}</h3>
                      <p className="text-gray-600 mb-2">{benefit.description}</p>
                      <span className="text-accent font-semibold">{benefit.stat}</span>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <h3 className="text-2xl font-bold text-primary mb-6 text-center">
                مقارنة سريعة
              </h3>
              <div className="space-y-6">
                {[
                  {
                    before: "إدارة يدوية للحجوزات",
                    after: "أتمتة كاملة للحجوزات"
                  },
                  {
                    before: "مكالمات هاتفية مستمرة",
                    after: "حجز أونلاين 24/7"
                  },
                  {
                    before: "أخطاء في الجدولة",
                    after: "جدولة ذكية بدون أخطاء"
                  },
                  {
                    before: "مواعيد فائتة كثيرة",
                    after: "تذكيرات تلقائية"
                  },
                  {
                    before: "صعوبة في التتبع",
                    after: "تقارير مفصلة ودقيقة"
                  }
                ].map((comparison, index) => (
                  <div key={index} className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
                      <div className="text-red-500 text-sm mb-1">❌ قبل</div>
                      <div className="text-xs text-gray-600">{comparison.before}</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                      <div className="text-green-500 text-sm mb-1">✅ بعد</div>
                      <div className="text-xs text-gray-600">{comparison.after}</div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الاشتراك
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب حجم أعمالك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "الخطة الأساسية",
                price: "199",
                period: "شهرياً",
                description: "مثالية للأعمال الصغيرة",
                features: [
                  "حتى 100 حجز شهرياً",
                  "موظف واحد",
                  "تذكيرات SMS",
                  "لوحة تحكم أساسية",
                  "دعم فني عبر البريد",
                  "تقارير أساسية"
                ]
              },
              {
                name: "الخطة المتقدمة",
                price: "499",
                period: "شهرياً",
                description: "الأنسب للأعمال المتوسطة",
                features: [
                  "حجوزات غير محدودة",
                  "حتى 5 موظفين",
                  "تذكيرات متعددة",
                  "تطبيق موبايل",
                  "دعم فني 24/7",
                  "تقارير متقدمة",
                  "تكاملات أساسية",
                  "نظام دفع"
                ],
                popular: true
              },
              {
                name: "الخطة الاحترافية",
                price: "999",
                period: "شهرياً",
                description: "للمؤسسات الكبيرة",
                features: [
                  "كل ميزات الخطة المتقدمة",
                  "موظفين غير محدودين",
                  "تخصيص كامل",
                  "API متقدم",
                  "مدير حساب مخصص",
                  "تدريب الفريق",
                  "تكاملات مخصصة",
                  "نسخ احتياطية يومية"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-2xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {plan.price} <span className="text-lg text-gray-600">ريال</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              جرب sybooking مجاناً لمدة 14 يوم
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              اكتشف كيف يمكن لـ sybooking أن يحول طريقة إدارتك للحجوزات
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                ابدأ التجربة المجانية
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع مختص
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default SybookingPage
