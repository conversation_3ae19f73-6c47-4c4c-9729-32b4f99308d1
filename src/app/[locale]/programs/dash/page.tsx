'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const DashPage = () => {
  const t = useTranslations('programs.dash')
  const locale = useLocale()
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">📊</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              dash
              <span className="block text-accent">{t('hero.subtitle')}</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اطلب عرض توضيحي
              </Link>
              <Link 
                href="#features"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف المميزات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('features.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('features.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {(locale === 'ar' ? [
              {
                icon: "📈",
                title: "تحليلات متقدمة",
                description: "تقارير مفصلة ورسوم بيانية تفاعلية لمتابعة أداء أعمالك"
              },
              {
                icon: "👥",
                title: "إدارة العملاء",
                description: "نظام CRM متكامل لإدارة علاقاتك مع العملاء وتتبع تفاعلاتهم"
              },
              {
                icon: "📦",
                title: "إدارة المخزون",
                description: "تتبع المنتجات والمخزون مع تنبيهات ذكية للكميات المنخفضة"
              },
              {
                icon: "💰",
                title: "إدارة المالية",
                description: "متابعة الإيرادات والمصروفات مع تقارير مالية شاملة"
              },
              {
                icon: "📋",
                title: "إدارة المشاريع",
                description: "تنظيم المهام والمشاريع مع تتبع التقدم والمواعيد النهائية"
              },
              {
                icon: "🔔",
                title: "التنبيهات الذكية",
                description: "إشعارات فورية للأحداث المهمة والمهام المطلوبة"
              },
              {
                icon: "📱",
                title: "متوافق مع الجوال",
                description: "واجهة متجاوبة تعمل بسلاسة على جميع الأجهزة"
              },
              {
                icon: "🔒",
                title: "أمان متقدم",
                description: "حماية قوية للبيانات مع نظام صلاحيات متعدد المستويات"
              },
              {
                icon: "🔗",
                title: "تكاملات واسعة",
                description: "ربط سهل مع الأنظمة والخدمات الأخرى التي تستخدمها"
              }
            ] : [
              {
                icon: "📈",
                title: "Advanced Analytics",
                description: "Detailed reports and interactive charts to monitor your business performance"
              },
              {
                icon: "👥",
                title: "Customer Management",
                description: "Integrated CRM system to manage customer relationships and track interactions"
              },
              {
                icon: "📦",
                title: "Inventory Management",
                description: "Track products and inventory with smart alerts for low quantities"
              },
              {
                icon: "💰",
                title: "Financial Management",
                description: "Monitor revenues and expenses with comprehensive financial reports"
              },
              {
                icon: "📋",
                title: "Project Management",
                description: "Organize tasks and projects with progress tracking and deadlines"
              },
              {
                icon: "🔔",
                title: "Smart Notifications",
                description: "Instant notifications for important events and required tasks"
              },
              {
                icon: "📱",
                title: "Mobile Compatible",
                description: "Responsive interface that works smoothly on all devices"
              },
              {
                icon: "🔒",
                title: "Advanced Security",
                description: "Strong data protection with multi-level permissions system"
              },
              {
                icon: "🔗",
                title: "Wide Integrations",
                description: "Easy connection with other systems and services you use"
              }
            ]).map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Who Is It For Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('suitableFor.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('suitableFor.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {(locale === 'ar' ? [
              {
                icon: "🏪",
                title: "المتاجر الإلكترونية",
                description: "إدارة شاملة للمنتجات والطلبات والعملاء"
              },
              {
                icon: "🏢",
                title: "الشركات الصغيرة والمتوسطة",
                description: "حلول إدارية متكاملة لتنظيم العمليات"
              },
              {
                icon: "🎯",
                title: "الوكالات التسويقية",
                description: "إدارة العملاء والحملات والمشاريع"
              },
              {
                icon: "🏥",
                title: "العيادات والمراكز الطبية",
                description: "إدارة المواعيد والمرضى والملفات الطبية"
              }
            ] : [
              {
                icon: "🏪",
                title: "E-commerce Stores",
                description: "Comprehensive management of products, orders, and customers"
              },
              {
                icon: "🏢",
                title: "Small and Medium Enterprises",
                description: "Integrated administrative solutions to organize operations"
              },
              {
                icon: "🎯",
                title: "Marketing Agencies",
                description: "Customer, campaign, and project management"
              },
              {
                icon: "🏥",
                title: "Clinics and Medical Centers",
                description: "Appointment, patient, and medical record management"
              }
            ]).map((target, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-5xl mb-4">{target.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{target.title}</h3>
                <p className="text-gray-600">{target.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('benefits.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('benefits.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="space-y-8">
                {[
                  {
                    icon: "⏱️",
                    title: "توفير الوقت",
                    description: "أتمتة المهام الروتينية ووضع العمليات في نظام واحد"
                  },
                  {
                    icon: "💡",
                    title: "قرارات أذكى",
                    description: "بيانات دقيقة وتحليلات عميقة لاتخاذ قرارات مدروسة"
                  },
                  {
                    icon: "📊",
                    title: "زيادة الكفاءة",
                    description: "تحسين الإنتاجية وتقليل الأخطاء البشرية"
                  },
                  {
                    icon: "🚀",
                    title: "نمو أسرع",
                    description: "أدوات تساعدك على توسيع أعمالك بثقة"
                  }
                ].map((benefit, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-3xl ml-4 flex-shrink-0">{benefit.icon}</div>
                    <div>
                      <h3 className="text-xl font-bold text-primary mb-2">{benefit.title}</h3>
                      <p className="text-gray-600">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <h3 className="text-2xl font-bold text-primary mb-6 text-center">
                إحصائيات مذهلة
              </h3>
              <div className="space-y-6">
                {[
                  { number: "75%", label: "توفير في الوقت" },
                  { number: "60%", label: "زيادة في الكفاءة" },
                  { number: "90%", label: "تحسن في دقة البيانات" },
                  { number: "50%", label: "تقليل في التكاليف التشغيلية" }
                ].map((stat, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-gray-600">{stat.label}</span>
                    <span className="text-2xl font-bold text-accent">{stat.number}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الاشتراك
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب حجم أعمالك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "الخطة الأساسية",
                price: "299",
                period: "شهرياً",
                description: "مثالية للشركات الناشئة",
                features: [
                  "حتى 5 مستخدمين",
                  "إدارة العملاء الأساسية",
                  "تقارير أساسية",
                  "دعم فني عبر البريد",
                  "مساحة تخزين 5 جيجا"
                ]
              },
              {
                name: "الخطة المتقدمة",
                price: "599",
                period: "شهرياً",
                description: "الأنسب للشركات المتوسطة",
                features: [
                  "حتى 25 مستخدم",
                  "جميع ميزات الخطة الأساسية",
                  "تحليلات متقدمة",
                  "تكاملات API",
                  "دعم فني على مدار الساعة",
                  "مساحة تخزين 50 جيجا",
                  "تخصيص الواجهة"
                ],
                popular: true
              },
              {
                name: "الخطة الشاملة",
                price: "1,199",
                period: "شهرياً",
                description: "للمؤسسات الكبيرة",
                features: [
                  "مستخدمين غير محدودين",
                  "جميع ميزات الخطة المتقدمة",
                  "تقارير مخصصة",
                  "تكاملات متقدمة",
                  "مدير حساب مخصص",
                  "مساحة تخزين غير محدودة",
                  "تدريب مخصص للفريق"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-2xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {plan.price} <span className="text-lg text-gray-600">ريال</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              جرب dash مجاناً لمدة 14 يوم
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              اكتشف كيف يمكن لـ dash أن يحول طريقة إدارتك لأعمالك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                ابدأ التجربة المجانية
              </Link>
              <Link
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                احجز عرض توضيحي
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default DashPage
