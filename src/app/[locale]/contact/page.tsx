'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    message: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.')
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">📞</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              تواصل معنا
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              نحن هنا لمساعدتك في تحقيق أهدافك الرقمية. تواصل معنا اليوم واحصل على استشارة مجانية
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Form & Info Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <h2 className="text-3xl font-bold text-primary mb-6">أرسل لنا رسالة</h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-gray-700 font-semibold mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors"
                      placeholder="أدخل اسمك الكامل"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-gray-700 font-semibold mb-2">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="phone" className="block text-gray-700 font-semibold mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      required
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors"
                      placeholder="+966 50 123 4567"
                    />
                  </div>
                  <div>
                    <label htmlFor="company" className="block text-gray-700 font-semibold mb-2">
                      اسم الشركة
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors"
                      placeholder="اسم شركتك (اختياري)"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="service" className="block text-gray-700 font-semibold mb-2">
                    الخدمة المطلوبة *
                  </label>
                  <select
                    id="service"
                    name="service"
                    required
                    value={formData.service}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors"
                  >
                    <option value="">اختر الخدمة</option>
                    <option value="web-development">تطوير المواقع والمتاجر</option>
                    <option value="mobile-development">تطوير تطبيقات الموبايل</option>
                    <option value="hosting">الاستضافة وإدارة السيرفرات</option>
                    <option value="automation">الأتمتة والتكاملات</option>
                    <option value="saas-growth">برنامج نمو SaaS</option>
                    <option value="dash">برنامج dash</option>
                    <option value="sybooking">برنامج sybooking</option>
                    <option value="sylink">برنامج sylink</option>
                    <option value="ease">برنامج ease</option>
                    <option value="syriana-bot">Syriana Bot</option>
                    <option value="consultation">استشارة عامة</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-gray-700 font-semibold mb-2">
                    تفاصيل المشروع *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent transition-colors resize-vertical"
                    placeholder="أخبرنا عن مشروعك ومتطلباتك بالتفصيل..."
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className="w-full bg-accent hover:bg-accent-600 text-white py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
                >
                  إرسال الرسالة
                </button>
              </form>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-primary mb-6">معلومات الاتصال</h3>
                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="text-2xl ml-4 text-accent">📧</div>
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-1">البريد الإلكتروني</h4>
                      <p className="text-gray-600"><EMAIL></p>
                      <p className="text-gray-600"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="text-2xl ml-4 text-accent">📱</div>
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-1">الهواتف</h4>
                      <p className="text-gray-600">مصر: +201066170179</p>
                      <p className="text-gray-600">مصر: +201025800940</p>
                      <p className="text-gray-600">بريطانيا: +44 7477 211088</p>
                      <p className="text-gray-600">أرضي: +20 57 2058006</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="text-2xl ml-4 text-accent">🏢</div>
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-1">الكيانات القانونية</h4>
                      <p className="text-gray-600">SouqProcare LTD (المملكة المتحدة)</p>
                      <p className="text-gray-600">Souq Pro Care.Com (مصر)</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-primary mb-6">ساعات العمل</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">الأحد - الخميس</span>
                    <span className="font-semibold text-gray-800">9:00 ص - 6:00 م</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الجمعة</span>
                    <span className="font-semibold text-gray-800">2:00 م - 6:00 م</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">السبت</span>
                    <span className="font-semibold text-gray-800">مغلق</span>
                  </div>
                </div>
                <div className="mt-4 p-4 bg-accent/10 rounded-lg">
                  <p className="text-sm text-accent font-semibold">
                    💡 الدعم الفني متاح 24/7 للعملاء المشتركين
                  </p>
                </div>
              </div>

              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold text-primary mb-6">تواصل سريع</h3>
                <div className="grid grid-cols-2 gap-4">
                  <a
                    href="https://wa.me/201066170179"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg text-center font-semibold transition-colors duration-300"
                  >
                    <div className="text-2xl mb-2">💬</div>
                    واتساب
                  </a>
                  <a
                    href="tel:+201066170179"
                    className="bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-lg text-center font-semibold transition-colors duration-300"
                  >
                    <div className="text-2xl mb-2">📞</div>
                    اتصال مباشر
                  </a>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              الأسئلة الشائعة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              إجابات على أكثر الأسئلة شيوعاً
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto space-y-6">
            {[
              {
                question: "كم يستغرق تطوير موقع إلكتروني؟",
                answer: "يعتمد على تعقيد المشروع، لكن عادة ما يستغرق من 2-8 أسابيع للمواقع العادية و 8-16 أسبوع للأنظمة المعقدة."
              },
              {
                question: "هل تقدمون دعم فني بعد التسليم؟",
                answer: "نعم، نقدم دعم فني مجاني لمدة تتراوح من 3-12 شهر حسب نوع المشروع، بالإضافة إلى خطط دعم مدفوعة."
              },
              {
                question: "ما هي طرق الدفع المتاحة؟",
                answer: "نقبل التحويل البنكي، الدفع الإلكتروني، والدفع على دفعات حسب مراحل المشروع."
              },
              {
                question: "هل يمكنني طلب تعديلات على المشروع؟",
                answer: "نعم، نقدم جولتين من التعديلات مجاناً، والتعديلات الإضافية متاحة بأسعار مناسبة."
              },
              {
                question: "هل تعملون مع عملاء من خارج السعودية؟",
                answer: "نعم، نخدم عملاء من جميع أنحاء العالم العربي وخارجه، مع دعم متعدد اللغات."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-gray-50 rounded-lg p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-primary mb-3">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default ContactPage
