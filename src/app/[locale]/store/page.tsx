'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import PriceDisplay from '@/components/ui/PriceDisplay'

const StorePage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const t = useTranslations('store')
  const locale = useLocale()
  const tButtons = useTranslations('buttons')

  const categories = [
    { id: 'all', name: t('products.categories.all'), icon: '🛍️' },
    { id: 'software', name: t('products.categories.software'), icon: '💻' },
    { id: 'templates', name: t('products.categories.templates'), icon: '🎨' },
    { id: 'services', name: t('products.categories.services'), icon: '🔧' },
    { id: 'hosting', name: t('products.categories.hosting'), icon: '☁️' },
    { id: 'domains', name: t('products.categories.domains'), icon: '🌐' }
  ]

  const products = [
    {
      id: 1,
      name: t('products.items.dash.name'),
      category: "software",
      price: 1999,
      originalPrice: 2999,
      currency: "SAR",
      description: t('products.items.dash.description'),
      features: locale === 'ar'
        ? ["إدارة شاملة", "تقارير متقدمة", "دعم فني", "تحديثات مجانية"]
        : ["Comprehensive Management", "Advanced Reports", "Technical Support", "Free Updates"],
      image: "📊",
      badge: t('products.items.dash.badge'),
      link: "/programs/dash"
    },
    {
      id: 2,
      name: t('products.items.sybooking.name'),
      category: "software",
      price: 1499,
      originalPrice: 1999,
      currency: "SAR",
      description: t('products.items.sybooking.description'),
      features: locale === 'ar'
        ? ["حجز أونلاين", "تذكيرات تلقائية", "إدارة العملاء", "تقارير مالية"]
        : ["Online Booking", "Automatic Reminders", "Customer Management", "Financial Reports"],
      image: "📅",
      badge: t('products.items.sybooking.badge'),
      link: "/programs/sybooking"
    },
    {
      id: 3,
      name: t('products.items.sylink.name'),
      category: "software",
      price: 799,
      originalPrice: 1199,
      currency: "SAR",
      description: t('products.items.sylink.description'),
      features: locale === 'ar'
        ? ["تحليل الروابط", "نطاقات مخصصة", "رموز QR", "إدارة الفرق"]
        : ["Link Analytics", "Custom Domains", "QR Codes", "Team Management"],
      image: "🔗",
      badge: t('products.items.sylink.badge'),
      link: "/programs/sylink"
    },
    {
      id: 4,
      name: t('products.items.ease.name'),
      category: "software",
      price: 1299,
      originalPrice: 1799,
      currency: "SAR",
      description: t('products.items.ease.description'),
      features: locale === 'ar'
        ? ["إدارة المهام", "تعاون الفريق", "تتبع الوقت", "تقارير التقدم"]
        : ["Task Management", "Team Collaboration", "Time Tracking", "Progress Reports"],
      image: "⚡",
      badge: t('products.items.ease.badge'),
      link: "/programs/ease"
    },
    {
      id: 5,
      name: t('products.items.syrianaBot.name'),
      category: "software",
      price: 2499,
      originalPrice: 3499,
      currency: "SAR",
      description: t('products.items.syrianaBot.description'),
      features: locale === 'ar'
        ? ["ردود ذكية", "متعدد المنصات", "تحليلات", "تدريب مخصص"]
        : ["AI Responses", "Multi-platform", "Analytics", "Custom Training"],
      image: "🤖",
      badge: t('products.items.syrianaBot.badge'),
      link: "/programs/syriana-bot"
    },
    {
      id: 6,
      name: t('products.items.ecommerceTemplate.name'),
      category: "templates",
      price: 499,
      originalPrice: 799,
      currency: "SAR",
      description: t('products.items.ecommerceTemplate.description'),
      features: locale === 'ar'
        ? ["تصميم متجاوب", "تكامل الدفع", "لوحة إدارة", "محسن لمحركات البحث"]
        : ["Responsive Design", "Payment Integration", "Admin Panel", "SEO Optimized"],
      image: "🛒",
      badge: t('products.items.ecommerceTemplate.badge'),
      link: "/store/ecommerce-template"
    },
    {
      id: 7,
      name: t('products.items.techCompanyTemplate.name'),
      category: "templates",
      price: 299,
      originalPrice: 499,
      currency: "SAR",
      description: t('products.items.techCompanyTemplate.description'),
      features: locale === 'ar'
        ? ["تصميم عصري", "قسم الأعمال", "نماذج التواصل", "متجاوب مع الجوال"]
        : ["Modern Design", "Portfolio Section", "Contact Forms", "Mobile Responsive"],
      image: "🏢",
      badge: t('products.items.techCompanyTemplate.badge'),
      link: "/store/tech-company-template"
    },
    {
      id: 8,
      name: t('products.items.webHosting.name'),
      category: "hosting",
      price: 199,
      originalPrice: null,
      currency: "SAR",
      description: t('products.items.webHosting.description'),
      features: locale === 'ar'
        ? ["99.9% وقت تشغيل", "دعم 24/7", "SSL مجاني", "نسخ احتياطية يومية"]
        : ["99.9% Uptime", "24/7 Support", "Free SSL", "Daily Backups"],
      image: "☁️",
      badge: t('products.items.webHosting.badge'),
      recurring: t('common.monthly'),
      link: "/services/hosting"
    },
    {
      id: 9,
      name: t('products.items.techConsulting.name'),
      category: "services",
      price: 299,
      originalPrice: null,
      currency: "SAR",
      description: t('products.items.techConsulting.description'),
      features: locale === 'ar'
        ? ["تحليل خبير", "حلول مخصصة", "دعم التنفيذ", "إرشاد مستمر"]
        : ["Expert Analysis", "Custom Solutions", "Implementation Support", "Ongoing Guidance"],
      image: "💡",
      badge: t('products.items.techConsulting.badge'),
      recurring: t('common.perSession'),
      link: "/contact"
    },
    {
      id: 10,
      name: t('products.items.premiumDomain.name'),
      category: "domains",
      price: 89,
      originalPrice: 129,
      currency: "SAR",
      description: t('products.items.premiumDomain.description'),
      features: locale === 'ar'
        ? ["امتداد مميز", "إدارة سهلة", "تحكم DNS", "دعم النقل"]
        : ["Premium Extension", "Easy Management", "DNS Control", "Transfer Support"],
      image: "🌐",
      badge: t('products.items.premiumDomain.badge'),
      recurring: t('common.yearly'),
      link: "/store/domains"
    }
  ]

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.category === selectedCategory)

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🛍️</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('title')}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="#products"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                {t('hero.browseProducts')}
              </Link>
              <Link
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                {t('hero.customRequest')}
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('whyShop.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('whyShop.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "✅",
                title: t('whyShop.guaranteedQuality.title'),
                description: t('whyShop.guaranteedQuality.description')
              },
              {
                icon: "🚀",
                title: t('whyShop.fastDelivery.title'),
                description: t('whyShop.fastDelivery.description')
              },
              {
                icon: "🛡️",
                title: t('whyShop.comprehensiveWarranty.title'),
                description: t('whyShop.comprehensiveWarranty.description')
              },
              {
                icon: "🎧",
                title: t('whyShop.continuousSupport.title'),
                description: t('whyShop.continuousSupport.description')
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section id="products" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('products.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              {t('products.subtitle')}
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-accent text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-xl ml-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift border border-gray-100 relative"
              >
                {product.badge && (
                  <div className="absolute top-4 left-4 bg-accent text-white text-xs font-semibold px-3 py-1 rounded-full z-10">
                    {product.badge}
                  </div>
                )}

                <div className="bg-gradient-to-br from-primary to-primary-600 h-32 flex items-center justify-center">
                  <div className="text-6xl">{product.image}</div>
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-bold text-primary mb-2 line-clamp-2">{product.name}</h3>
                  <p className="text-gray-600 mb-4 text-sm line-clamp-2">{product.description}</p>

                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <PriceDisplay
                        amount={product.price}
                        originalCurrency={product.currency}
                        className="text-2xl font-bold text-accent"
                      />
                      {product.originalPrice && (
                        <PriceDisplay
                          amount={product.originalPrice}
                          originalCurrency={product.currency}
                          className="text-gray-500 line-through text-sm"
                        />
                      )}
                      {product.recurring && (
                        <span className="text-gray-500 text-sm">/ {product.recurring}</span>
                      )}
                    </div>
                    {product.originalPrice && (
                      <div className="text-green-600 text-sm font-semibold">
                        {t('common.save')} <PriceDisplay
                          amount={product.originalPrice - product.price}
                          originalCurrency={product.currency}
                          className="inline"
                        />
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">{t('common.features')}:</h4>
                    <ul className="space-y-1">
                      {product.features.slice(0, 3).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-xs text-gray-600">
                          <div className="w-1.5 h-1.5 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex gap-2">
                    <Link
                      href={product.link}
                      className="flex-1 bg-accent hover:bg-accent-600 text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-colors duration-300"
                    >
                      {t('common.moreDetails')}
                    </Link>
                    <Link
                      href="/contact"
                      className="flex-1 border-2 border-accent text-accent hover:bg-accent hover:text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-all duration-300"
                    >
                      {t('common.orderNow')}
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Special Offers */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              عروض خاصة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              استفد من عروضنا المحدودة واحصل على أفضل الأسعار
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-accent to-accent-600 rounded-2xl p-8 text-white"
            >
              <div className="text-4xl mb-4">🎉</div>
              <h3 className="text-2xl font-bold mb-4">باقة البداية الذكية</h3>
              <p className="mb-6">احصل على أي برنامج + استضافة + نطاق بسعر مخفض</p>
              <div className="text-3xl font-bold mb-4">2,999 ريال بدلاً من 4,499</div>
              <Link
                href="/contact"
                className="bg-white text-accent hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold transition-colors duration-300 inline-block"
              >
                احصل على العرض
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-primary to-primary-600 rounded-2xl p-8 text-white"
            >
              <div className="text-4xl mb-4">💎</div>
              <h3 className="text-2xl font-bold mb-4">باقة الأعمال المتكاملة</h3>
              <p className="mb-6">جميع برامجنا + خدمات التطوير المخصص + دعم سنوي</p>
              <div className="text-3xl font-bold mb-4">9,999 ريال بدلاً من 15,999</div>
              <Link
                href="/contact"
                className="bg-white text-primary hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold transition-colors duration-300 inline-block"
              >
                احصل على العرض
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('faq.title')}
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {t('faq.items').map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-6"
              >
                <h3 className="text-lg font-bold text-primary mb-3">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {t('cta.title')}
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              {t('cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                {t('cta.customRequest')}
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                {t('cta.talkToUs')}
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default StorePage
