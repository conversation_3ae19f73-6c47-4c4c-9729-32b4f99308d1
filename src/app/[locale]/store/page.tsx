'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import { useTranslations } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import PriceDisplay from '@/components/ui/PriceDisplay'

const StorePage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const t = useTranslations('store')
  const tButtons = useTranslations('buttons')

  const categories = [
    { id: 'all', name: 'جميع المنتجات', icon: '🛍️' },
    { id: 'software', name: 'البرامج', icon: '💻' },
    { id: 'templates', name: 'القوالب', icon: '🎨' },
    { id: 'services', name: 'الخدمات', icon: '🔧' },
    { id: 'hosting', name: 'الاستضافة', icon: '☁️' },
    { id: 'domains', name: 'النطاقات', icon: '🌐' }
  ]

  const products = [
    {
      id: 1,
      name: "dash - لوحة التحكم الشاملة",
      category: "software",
      price: 1999,
      originalPrice: 2999,
      currency: "SAR",
      description: "منصة إدارة متكاملة لجميع أعمالك التجارية",
      features: ["إدارة شاملة", "تقارير متقدمة", "دعم فني", "تحديثات مجانية"],
      image: "📊",
      badge: "الأكثر مبيعاً",
      link: "/programs/dash"
    },
    {
      id: 2,
      name: "sybooking - نظام الحجوزات",
      category: "software",
      price: 1499,
      originalPrice: 1999,
      currency: "SAR",
      description: "نظام حجوزات ذكي للعيادات والصالونات",
      features: ["حجز أونلاين", "تذكيرات تلقائية", "إدارة العملاء", "تقارير مالية"],
      image: "📅",
      badge: "جديد",
      link: "/programs/sybooking"
    },
    {
      id: 3,
      name: "sylink - إدارة الروابط الذكية",
      category: "software",
      price: 799,
      originalPrice: 1199,
      currency: "SAR",
      description: "منصة احترافية لإدارة وتتبع الروابط المختصرة",
      features: ["روابط مخصصة", "تحليلات متقدمة", "QR Codes", "استهداف جغرافي"],
      image: "🔗",
      link: "/programs/sylink"
    },
    {
      id: 4,
      name: "ease - إدارة المشاريع",
      category: "software",
      price: 1299,
      originalPrice: 1799,
      currency: "SAR",
      description: "أداة بسيطة وقوية لإدارة المشاريع والفرق",
      features: ["إدارة المهام", "تعاون الفريق", "تتبع الوقت", "تقارير الإنتاجية"],
      image: "⚡",
      link: "/programs/ease"
    },
    {
      id: 5,
      name: "Syriana Bot - البوت الذكي",
      category: "software",
      price: 2499,
      originalPrice: 3499,
      currency: "SAR",
      description: "مساعد ذكي لأتمتة خدمة العملاء والمبيعات",
      features: ["ذكاء اصطناعي", "دعم متعدد المنصات", "تعلم آلي", "تكاملات متقدمة"],
      image: "🤖",
      badge: "مميز",
      link: "/programs/syriana-bot"
    },
    {
      id: 6,
      name: "قالب متجر إلكتروني احترافي",
      category: "templates",
      price: 499,
      originalPrice: 799,
      currency: "SAR",
      description: "قالب متجر إلكتروني كامل مع لوحة إدارة",
      features: ["تصميم متجاوب", "نظام دفع", "إدارة المنتجات", "SEO محسن"],
      image: "🛒",
      link: "/store/ecommerce-template"
    },
    {
      id: 7,
      name: "قالب موقع شركة تقنية",
      category: "templates",
      price: 299,
      originalPrice: 499,
      currency: "SAR",
      description: "قالب موقع احترافي للشركات التقنية",
      features: ["تصميم حديث", "صفحات متعددة", "نماذج تفاعلية", "محسن للسرعة"],
      image: "🏢",
      link: "/store/tech-company-template"
    },
    {
      id: 8,
      name: "استضافة ويب احترافية",
      category: "hosting",
      price: 199,
      originalPrice: null,
      currency: "SAR",
      description: "استضافة سحابية عالية الأداء مع دعم فني 24/7",
      features: ["SSD سريع", "SSL مجاني", "نسخ احتياطية", "دعم فني"],
      image: "☁️",
      recurring: "شهرياً",
      link: "/services/hosting"
    },
    {
      id: 9,
      name: "استشارة تقنية متخصصة",
      category: "services",
      price: 299,
      originalPrice: null,
      currency: "SAR",
      description: "جلسة استشارة تقنية مع خبرائنا لمدة ساعة",
      features: ["استشارة مخصصة", "تحليل المتطلبات", "خطة عمل", "توصيات تقنية"],
      image: "💡",
      recurring: "لكل جلسة",
      link: "/contact"
    },
    {
      id: 10,
      name: "نطاق .com مميز",
      category: "domains",
      price: 89,
      originalPrice: 129,
      currency: "SAR",
      description: "نطاق .com لمدة سنة مع حماية الخصوصية",
      features: ["تسجيل سنوي", "حماية الخصوصية", "إدارة DNS", "دعم فني"],
      image: "🌐",
      recurring: "سنوياً",
      link: "/store/domains"
    }
  ]

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.category === selectedCategory)

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🛍️</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('title')}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="#products"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                {t('hero.browseProducts')}
              </Link>
              <Link
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                {t('hero.customRequest')}
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا تتسوق معنا؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نقدم منتجات وخدمات عالية الجودة مع ضمان الرضا التام
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "✅",
                title: "جودة مضمونة",
                description: "جميع منتجاتنا مختبرة ومضمونة الجودة"
              },
              {
                icon: "🚀",
                title: "تسليم سريع",
                description: "تسليم فوري للمنتجات الرقمية"
              },
              {
                icon: "🛡️",
                title: "ضمان شامل",
                description: "ضمان استرداد المال خلال 30 يوم"
              },
              {
                icon: "🎧",
                title: "دعم مستمر",
                description: "دعم فني متخصص على مدار الساعة"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section id="products" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              منتجاتنا وخدماتنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              اختر من مجموعة واسعة من المنتجات والخدمات التقنية
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-accent text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-xl ml-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift border border-gray-100 relative"
              >
                {product.badge && (
                  <div className="absolute top-4 left-4 bg-accent text-white text-xs font-semibold px-3 py-1 rounded-full z-10">
                    {product.badge}
                  </div>
                )}

                <div className="bg-gradient-to-br from-primary to-primary-600 h-32 flex items-center justify-center">
                  <div className="text-6xl">{product.image}</div>
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-bold text-primary mb-2 line-clamp-2">{product.name}</h3>
                  <p className="text-gray-600 mb-4 text-sm line-clamp-2">{product.description}</p>

                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <PriceDisplay
                        amount={product.price}
                        originalCurrency={product.currency}
                        className="text-2xl font-bold text-accent"
                      />
                      {product.originalPrice && (
                        <PriceDisplay
                          amount={product.originalPrice}
                          originalCurrency={product.currency}
                          className="text-gray-500 line-through text-sm"
                        />
                      )}
                      {product.recurring && (
                        <span className="text-gray-500 text-sm">/ {product.recurring}</span>
                      )}
                    </div>
                    {product.originalPrice && (
                      <div className="text-green-600 text-sm font-semibold">
                        وفر <PriceDisplay
                          amount={product.originalPrice - product.price}
                          originalCurrency={product.currency}
                          className="inline"
                        />
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">المميزات:</h4>
                    <ul className="space-y-1">
                      {product.features.slice(0, 3).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-xs text-gray-600">
                          <div className="w-1.5 h-1.5 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex gap-2">
                    <Link
                      href={product.link}
                      className="flex-1 bg-accent hover:bg-accent-600 text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-colors duration-300"
                    >
                      تفاصيل أكثر
                    </Link>
                    <Link
                      href="/contact"
                      className="flex-1 border-2 border-accent text-accent hover:bg-accent hover:text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-all duration-300"
                    >
                      اطلب الآن
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Special Offers */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              عروض خاصة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              استفد من عروضنا المحدودة واحصل على أفضل الأسعار
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-accent to-accent-600 rounded-2xl p-8 text-white"
            >
              <div className="text-4xl mb-4">🎉</div>
              <h3 className="text-2xl font-bold mb-4">باقة البداية الذكية</h3>
              <p className="mb-6">احصل على أي برنامج + استضافة + نطاق بسعر مخفض</p>
              <div className="text-3xl font-bold mb-4">2,999 ريال بدلاً من 4,499</div>
              <Link
                href="/contact"
                className="bg-white text-accent hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold transition-colors duration-300 inline-block"
              >
                احصل على العرض
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-r from-primary to-primary-600 rounded-2xl p-8 text-white"
            >
              <div className="text-4xl mb-4">💎</div>
              <h3 className="text-2xl font-bold mb-4">باقة الأعمال المتكاملة</h3>
              <p className="mb-6">جميع برامجنا + خدمات التطوير المخصص + دعم سنوي</p>
              <div className="text-3xl font-bold mb-4">9,999 ريال بدلاً من 15,999</div>
              <Link
                href="/contact"
                className="bg-white text-primary hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold transition-colors duration-300 inline-block"
              >
                احصل على العرض
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              أسئلة شائعة حول المتجر
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {[
              {
                question: "كيف يتم التسليم؟",
                answer: "المنتجات الرقمية يتم تسليمها فوراً عبر البريد الإلكتروني، والخدمات يتم البدء فيها خلال 24 ساعة."
              },
              {
                question: "هل يمكنني استرداد المال؟",
                answer: "نعم، نقدم ضمان استرداد المال خلال 30 يوم من تاريخ الشراء إذا لم تكن راضياً عن المنتج."
              },
              {
                question: "هل تقدمون دعم فني؟",
                answer: "نعم، نقدم دعم فني شامل لجميع منتجاتنا مع ضمان الاستجابة خلال 24 ساعة."
              },
              {
                question: "هل يمكن تخصيص المنتجات؟",
                answer: "بالطبع! يمكننا تخصيص أي منتج ليناسب احتياجاتك الخاصة مقابل رسوم إضافية."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-6"
              >
                <h3 className="text-lg font-bold text-primary mb-3">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد للبدء؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              اختر المنتج المناسب لك أو تواصل معنا للحصول على حل مخصص
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                طلب مخصص
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث معنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default StorePage
