'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const AutomationPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🤖</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              الأتمتة والتكاملات
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              حلول أتمتة ذكية وتكاملات متقدمة تساعدك على توفير الوقت وزيادة الكفاءة وتحسين الإنتاجية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                احصل على استشارة مجانية
              </Link>
              <Link 
                href="#solutions"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف الحلول
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* What We Automate */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              ما الذي يمكننا أتمتته؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نحول العمليات اليدوية المتكررة إلى أنظمة ذكية تعمل تلقائياً
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📧",
                title: "التسويق الإلكتروني",
                description: "أتمتة الحملات التسويقية والرسائل الإلكترونية",
                examples: ["إرسال رسائل ترحيبية", "متابعة العملاء المحتملين", "حملات إعادة الاستهداف", "تقارير الأداء التلقائية"]
              },
              {
                icon: "👥",
                title: "إدارة العملاء",
                description: "أتمتة عمليات خدمة العملاء والمتابعة",
                examples: ["الرد التلقائي على الاستفسارات", "تصنيف التذاكر", "متابعة رضا العملاء", "إدارة قاعدة بيانات العملاء"]
              },
              {
                icon: "💰",
                title: "العمليات المالية",
                description: "أتمتة الفواتير والمدفوعات والتقارير المالية",
                examples: ["إنشاء الفواتير التلقائية", "متابعة المدفوعات", "التقارير المالية", "إدارة المصروفات"]
              },
              {
                icon: "📦",
                title: "إدارة المخزون",
                description: "أتمتة تتبع المخزون والطلبات والتوريد",
                examples: ["تتبع مستويات المخزون", "طلبات التوريد التلقائية", "إدارة الطلبات", "تقارير المبيعات"]
              },
              {
                icon: "📊",
                title: "التقارير والتحليلات",
                description: "إنشاء تقارير تلقائية وتحليلات متقدمة",
                examples: ["تقارير الأداء اليومية", "تحليل البيانات", "لوحات المعلومات", "التنبؤات الذكية"]
              },
              {
                icon: "🔄",
                title: "سير العمل",
                description: "أتمتة العمليات الداخلية وسير العمل",
                examples: ["موافقات المشاريع", "إدارة المهام", "تنسيق الفرق", "متابعة الإنجاز"]
              }
            ].map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-4xl mb-4">{category.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{category.title}</h3>
                <p className="text-gray-600 mb-4">{category.description}</p>
                <ul className="space-y-2">
                  {category.examples.map((example, exampleIndex) => (
                    <li key={exampleIndex} className="flex items-start text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-2 flex-shrink-0"></div>
                      {example}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Integration Solutions */}
      <section id="solutions" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              حلول التكامل المتقدمة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نربط جميع أنظمتك وتطبيقاتك في نظام واحد متكامل
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h3 className="text-2xl font-bold text-primary mb-6">منصات التكامل الشائعة</h3>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { name: "Zapier", icon: "⚡" },
                  { name: "Microsoft Power Automate", icon: "🔄" },
                  { name: "Google Workspace", icon: "📧" },
                  { name: "Slack", icon: "💬" },
                  { name: "Salesforce", icon: "☁️" },
                  { name: "HubSpot", icon: "🎯" },
                  { name: "Shopify", icon: "🛒" },
                  { name: "WooCommerce", icon: "🛍️" },
                  { name: "PayPal", icon: "💳" },
                  { name: "Stripe", icon: "💰" },
                  { name: "WhatsApp Business", icon: "📱" },
                  { name: "Telegram", icon: "✈️" }
                ].map((platform, index) => (
                  <div
                    key={index}
                    className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-accent/10 transition-colors duration-300"
                  >
                    <span className="text-2xl ml-3">{platform.icon}</span>
                    <span className="text-sm font-medium text-gray-700">{platform.name}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gradient-to-br from-primary to-primary-600 text-white rounded-2xl p-8"
            >
              <h3 className="text-2xl font-bold mb-6">فوائد التكامل</h3>
              <div className="space-y-4">
                {[
                  {
                    icon: "⏱️",
                    title: "توفير الوقت",
                    description: "تقليل الوقت المطلوب للمهام اليدوية بنسبة تصل إلى 80%"
                  },
                  {
                    icon: "🎯",
                    title: "دقة أعلى",
                    description: "تقليل الأخطاء البشرية وضمان دقة البيانات"
                  },
                  {
                    icon: "📈",
                    title: "زيادة الإنتاجية",
                    description: "تحسين الكفاءة وزيادة الإنتاجية بشكل ملحوظ"
                  },
                  {
                    icon: "💡",
                    title: "رؤى أفضل",
                    description: "بيانات موحدة وتقارير شاملة لاتخاذ قرارات أذكى"
                  }
                ].map((benefit, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-2xl ml-4 flex-shrink-0">{benefit.icon}</div>
                    <div>
                      <h4 className="font-semibold mb-1">{benefit.title}</h4>
                      <p className="text-white/90 text-sm">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              كيف نعمل؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              منهجية مدروسة لضمان نجاح مشروع الأتمتة
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "التحليل والتقييم",
                description: "دراسة العمليات الحالية وتحديد فرص الأتمتة",
                icon: "🔍"
              },
              {
                step: "02",
                title: "التصميم والتخطيط",
                description: "وضع خطة شاملة للأتمتة والتكاملات المطلوبة",
                icon: "📋"
              },
              {
                step: "03",
                title: "التطوير والتنفيذ",
                description: "بناء الحلول وتطبيق الأتمتة خطوة بخطوة",
                icon: "⚙️"
              },
              {
                step: "04",
                title: "الاختبار والتحسين",
                description: "اختبار شامل وتحسين مستمر للأداء",
                icon: "🚀"
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">
                  {process.step}
                </div>
                <div className="text-4xl mb-4">{process.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              قصص نجاح حقيقية
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نتائج ملموسة حققناها لعملائنا
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                company: "شركة تجارة إلكترونية",
                challenge: "إدارة يدوية للطلبات والمخزون",
                solution: "أتمتة كاملة لسلسلة التوريد",
                result: "توفير 15 ساعة عمل أسبوعياً وزيادة الدقة بنسبة 95%",
                icon: "🛒"
              },
              {
                company: "وكالة تسويق رقمي",
                challenge: "تقارير يدوية معقدة للعملاء",
                solution: "تكامل جميع منصات التسويق في نظام واحد",
                result: "تقليل وقت إعداد التقارير من 8 ساعات إلى 30 دقيقة",
                icon: "📊"
              },
              {
                company: "عيادة طبية",
                challenge: "إدارة المواعيد والمتابعة اليدوية",
                solution: "نظام أتمتة شامل للمواعيد والتذكيرات",
                result: "تحسين رضا المرضى بنسبة 40% وتقليل المواعيد الفائتة",
                icon: "🏥"
              }
            ].map((story, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4 text-center">{story.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{story.company}</h3>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="font-semibold text-gray-700">التحدي: </span>
                    <span className="text-gray-600">{story.challenge}</span>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-700">الحل: </span>
                    <span className="text-gray-600">{story.solution}</span>
                  </div>
                  <div className="p-3 bg-accent/10 rounded-lg">
                    <span className="font-semibold text-accent">النتيجة: </span>
                    <span className="text-gray-700">{story.result}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              التقنيات التي نستخدمها
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              أحدث التقنيات والأدوات لضمان أفضل النتائج
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              { name: "Python", icon: "🐍" },
              { name: "Node.js", icon: "🟢" },
              { name: "API Integration", icon: "🔗" },
              { name: "Webhooks", icon: "🪝" },
              { name: "Cloud Functions", icon: "☁️" },
              { name: "Database", icon: "🗄️" },
              { name: "AI/ML", icon: "🤖" },
              { name: "RPA", icon: "🔄" },
              { name: "Microservices", icon: "🧩" },
              { name: "Docker", icon: "🐳" },
              { name: "Kubernetes", icon: "⚙️" },
              { name: "Monitoring", icon: "📊" }
            ].map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-xl p-4 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-3xl mb-2">{tech.icon}</div>
                <div className="text-sm font-medium text-gray-700">{tech.name}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لأتمتة أعمالك؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              دعنا نساعدك في توفير الوقت وزيادة الكفاءة من خلال حلول الأتمتة المتقدمة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احصل على تحليل مجاني
              </Link>
              <Link
                href="https://wa.me/201066170179"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع خبير
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default AutomationPage
