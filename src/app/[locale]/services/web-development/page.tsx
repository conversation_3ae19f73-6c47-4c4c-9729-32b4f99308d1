'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const WebDevelopmentPage = () => {
  const t = useTranslations('services.webDevelopment')
  const locale = useLocale()
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🌐</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('hero.title')}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اطلب عرض سعر
              </Link>
              <Link 
                href="/portfolio"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                شاهد أعمالنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              ما نقدمه لك
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              حلول شاملة لجميع احتياجاتك الرقمية
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🎨",
                title: "تصميم احترافي",
                description: "تصاميم عصرية وجذابة تعكس هوية علامتك التجارية"
              },
              {
                icon: "📱",
                title: "متجاوب بالكامل",
                description: "يعمل بشكل مثالي على جميع الأجهزة والشاشات"
              },
              {
                icon: "⚡",
                title: "سرعة عالية",
                description: "تحسين الأداء لضمان تحميل سريع وتجربة مستخدم ممتازة"
              },
              {
                icon: "🔒",
                title: "أمان متقدم",
                description: "حماية قوية لبياناتك وبيانات عملائك"
              },
              {
                icon: "🛒",
                title: "نظام دفع متكامل",
                description: "تكامل مع جميع بوابات الدفع المحلية والعالمية"
              },
              {
                icon: "📊",
                title: "تحليلات متقدمة",
                description: "تقارير مفصلة عن الأداء والمبيعات"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              عملية التطوير
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نتبع منهجية مدروسة لضمان تسليم مشروع يفوق توقعاتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "التخطيط والتحليل",
                description: "دراسة متطلباتك وتحليل السوق المستهدف"
              },
              {
                step: "02", 
                title: "التصميم والنماذج",
                description: "إنشاء تصاميم تفاعلية ونماذج أولية"
              },
              {
                step: "03",
                title: "التطوير والبرمجة", 
                description: "تحويل التصاميم إلى موقع فعال وقابل للاستخدام"
              },
              {
                step: "04",
                title: "الاختبار والإطلاق",
                description: "اختبارات شاملة ثم إطلاق الموقع رسمياً"
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">
                  {process.step}
                </div>
                <h3 className="text-xl font-bold text-primary mb-3">{process.title}</h3>
                <p className="text-gray-600">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              باقات الخدمة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الباقة التي تناسب احتياجاتك وميزانيتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "الباقة الأساسية",
                price: "2,500",
                description: "مثالية للشركات الناشئة",
                features: [
                  "تصميم احترافي",
                  "5 صفحات رئيسية",
                  "متجاوب مع الجوال",
                  "نموذج تواصل",
                  "تحسين محركات البحث الأساسي"
                ]
              },
              {
                name: "الباقة المتقدمة",
                price: "5,000",
                description: "الأنسب للمتاجر الإلكترونية",
                features: [
                  "كل ما في الباقة الأساسية",
                  "نظام إدارة المحتوى",
                  "متجر إلكتروني كامل",
                  "تكامل بوابات الدفع",
                  "لوحة تحكم إدارية",
                  "دعم فني لمدة 6 أشهر"
                ],
                popular: true
              },
              {
                name: "الباقة الشاملة",
                price: "10,000",
                description: "للمشاريع الكبيرة والمعقدة",
                features: [
                  "كل ما في الباقة المتقدمة",
                  "تطبيق جوال مجاني",
                  "تكاملات متقدمة",
                  "تحليلات مفصلة",
                  "دعم فني لمدة سنة",
                  "تدريب الفريق"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-2xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {plan.price} <span className="text-lg text-gray-600">ريال</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  اطلب الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لبدء مشروعك؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احصل على استشارة مجانية واكتشف كيف يمكننا مساعدتك في تحقيق أهدافك الرقمية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احصل على استشارة مجانية
              </Link>
              <Link
                href="tel:+201066170179"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اتصل بنا الآن
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default WebDevelopmentPage
