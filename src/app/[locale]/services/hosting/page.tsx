'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const HostingPage = () => {
  const t = useTranslations('services.hosting')
  const locale = useLocale()
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🌐</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('hero.title')}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اطلب استشارة مجانية
              </Link>
              <Link 
                href="#plans"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                شاهد الباقات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('services.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('services.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "🌐",
                title: "استضافة المواقع",
                description: "استضافة سريعة وموثوقة لجميع أنواع المواقع",
                features: ["SSD عالي السرعة", "SSL مجاني", "نسخ احتياطي يومي", "دعم فني 24/7"]
              },
              {
                icon: "☁️",
                title: "الاستضافة السحابية",
                description: "حلول سحابية مرنة وقابلة للتوسع",
                features: ["موارد قابلة للتوسع", "توزيع جغرافي", "أداء عالي", "مرونة كاملة"]
              },
              {
                icon: "🖥️",
                title: "السيرفرات المخصصة",
                description: "سيرفرات مخصصة بالكامل لمشاريعك الكبيرة",
                features: ["تحكم كامل", "أداء مضمون", "أمان متقدم", "إدارة احترافية"]
              },
              {
                icon: "🔧",
                title: "إدارة السيرفرات",
                description: "إدارة شاملة لسيرفراتك مع مراقبة مستمرة",
                features: ["مراقبة 24/7", "تحديثات أمنية", "تحسين الأداء", "دعم فوري"]
              },
              {
                icon: "🛡️",
                title: "الحماية والأمان",
                description: "حلول أمنية متقدمة لحماية بياناتك",
                features: ["جدار حماية", "حماية DDoS", "مراقبة أمنية", "نسخ احتياطية"]
              },
              {
                icon: "📊",
                title: "المراقبة والتحليل",
                description: "مراقبة مستمرة وتقارير مفصلة عن الأداء",
                features: ["مراقبة الأداء", "تقارير مفصلة", "تنبيهات فورية", "تحليلات متقدمة"]
              }
            ].map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-4">{service.description}</p>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا تختار استضافتنا؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              مميزات تجعلنا الخيار الأول للشركات والمطورين
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "⚡",
                title: "سرعة فائقة",
                description: "أقراص SSD وشبكة CDN عالمية لأسرع تحميل"
              },
              {
                icon: "🔒",
                title: "أمان متقدم",
                description: "حماية قوية ضد جميع أنواع التهديدات"
              },
              {
                icon: "📈",
                title: "99.9% وقت تشغيل",
                description: "ضمان استمرارية الخدمة مع أقل انقطاع ممكن"
              },
              {
                icon: "🛠️",
                title: "دعم فني متخصص",
                description: "فريق دعم فني متاح 24/7 باللغة العربية"
              },
              {
                icon: "💰",
                title: "أسعار تنافسية",
                description: "أفضل قيمة مقابل المال مع باقات مرنة"
              },
              {
                icon: "🌍",
                title: "مراكز بيانات عالمية",
                description: "خوادم في مواقع استراتيجية حول العالم"
              },
              {
                icon: "🔄",
                title: "نسخ احتياطي تلقائي",
                description: "نسخ احتياطية يومية تلقائية لحماية بياناتك"
              },
              {
                icon: "📱",
                title: "لوحة تحكم سهلة",
                description: "واجهة بسيطة وسهلة الاستخدام لإدارة خدماتك"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="text-center bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Hosting Plans */}
      <section id="plans" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              باقات الاستضافة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الباقة التي تناسب احتياجاتك وميزانيتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "الباقة الأساسية",
                price: "99",
                period: "شهرياً",
                description: "مثالية للمواقع الشخصية والصغيرة",
                features: [
                  "مساحة تخزين 10 جيجا",
                  "نقل بيانات 100 جيجا",
                  "5 حسابات بريد إلكتروني",
                  "SSL مجاني",
                  "نسخ احتياطي أسبوعي",
                  "دعم فني عبر البريد"
                ]
              },
              {
                name: "الباقة المتقدمة",
                price: "299",
                period: "شهرياً",
                description: "الأنسب للشركات المتوسطة",
                features: [
                  "مساحة تخزين 50 جيجا",
                  "نقل بيانات غير محدود",
                  "حسابات بريد غير محدودة",
                  "SSL متقدم",
                  "نسخ احتياطي يومي",
                  "دعم فني 24/7",
                  "CDN مجاني",
                  "حماية DDoS"
                ],
                popular: true
              },
              {
                name: "الباقة الاحترافية",
                price: "599",
                period: "شهرياً",
                description: "للمشاريع الكبيرة والمتاجر",
                features: [
                  "مساحة تخزين 200 جيجا",
                  "نقل بيانات غير محدود",
                  "موارد مخصصة",
                  "SSL متقدم + Wildcard",
                  "نسخ احتياطي كل 6 ساعات",
                  "دعم فني أولوية",
                  "CDN متقدم",
                  "حماية أمنية شاملة",
                  "مراقبة مستمرة"
                ]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 ${
                  plan.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full text-center mb-4">
                    الأكثر شيوعاً
                  </div>
                )}
                <h3 className="text-2xl font-bold text-primary mb-2">{plan.name}</h3>
                <div className="text-3xl font-bold text-accent mb-2">
                  {plan.price} <span className="text-lg text-gray-600">ريال</span>
                </div>
                <p className="text-gray-600 mb-6">{plan.description}</p>
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.popular
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  اطلب الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Specifications */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              المواصفات التقنية
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              تقنيات متقدمة لضمان أفضل أداء وأمان
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: "الخوادم",
                specs: [
                  "معالجات Intel Xeon",
                  "ذاكرة DDR4 ECC",
                  "أقراص NVMe SSD",
                  "شبكة 10Gbps"
                ]
              },
              {
                title: "نظام التشغيل",
                specs: [
                  "Linux CentOS/Ubuntu",
                  "Windows Server",
                  "CloudLinux",
                  "تحديثات أمنية تلقائية"
                ]
              },
              {
                title: "لوحات التحكم",
                specs: [
                  "cPanel/WHM",
                  "Plesk",
                  "DirectAdmin",
                  "لوحة تحكم مخصصة"
                ]
              },
              {
                title: "قواعد البيانات",
                specs: [
                  "MySQL 8.0",
                  "PostgreSQL",
                  "MongoDB",
                  "Redis Cache"
                ]
              }
            ].map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6"
              >
                <h3 className="text-xl font-bold text-primary mb-4">{category.title}</h3>
                <ul className="space-y-2">
                  {category.specs.map((spec, specIndex) => (
                    <li key={specIndex} className="flex items-center text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                      <span className="text-sm">{spec}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لنقل موقعك إلى المستوى التالي؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احصل على استشارة مجانية واكتشف الحل الأمثل لاحتياجاتك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احصل على استشارة مجانية
              </Link>
              <Link
                href="https://wa.me/201066170179"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تواصل عبر واتساب
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default HostingPage
