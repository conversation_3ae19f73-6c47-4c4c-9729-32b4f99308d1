'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const ServicesPage = () => {
  const t = useTranslations('services')
  const locale = useLocale()
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🚀</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('hero.title')}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                احصل على استشارة مجانية
              </Link>
              <Link 
                href="#services"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تصفح الخدمات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا تختار خدماتنا؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نجمع بين الخبرة التقنية العميقة والفهم الشامل لاحتياجات الأعمال
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {(locale === 'ar' ? [
              {
                icon: "⚡",
                title: "سرعة في التنفيذ",
                description: "نلتزم بالمواعيد المحددة ونسلم المشاريع في الوقت المناسب"
              },
              {
                icon: "🎯",
                title: "حلول مخصصة",
                description: "نصمم كل حل ليناسب احتياجاتك الخاصة وأهداف عملك"
              },
              {
                icon: "🛡️",
                title: "أمان وموثوقية",
                description: "نطبق أعلى معايير الأمان والحماية في جميع مشاريعنا"
              },
              {
                icon: "🤝",
                title: "دعم مستمر",
                description: "نقدم الدعم الفني والصيانة المستمرة بعد التسليم"
              }
            ] : [
              {
                icon: "⚡",
                title: "Fast Execution",
                description: "We commit to deadlines and deliver projects on time"
              },
              {
                icon: "🎯",
                title: "Custom Solutions",
                description: "We design each solution to fit your specific needs and business goals"
              },
              {
                icon: "🛡️",
                title: "Security & Reliability",
                description: "We apply the highest security and protection standards in all our projects"
              },
              {
                icon: "🤝",
                title: "Continuous Support",
                description: "We provide ongoing technical support and maintenance after delivery"
              }
            ]).map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Main Services */}
      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('main.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('main.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {[
              {
                title: "تطوير المواقع والمتاجر الإلكترونية",
                description: "نصمم وننشئ مواقع ومتاجر إلكترونية احترافية تحقق أهدافك التجارية",
                icon: "🌐",
                features: [
                  "مواقع ويب متجاوبة وسريعة",
                  "متاجر إلكترونية متكاملة",
                  "أنظمة إدارة المحتوى",
                  "تحسين محركات البحث SEO",
                  "تكامل مع وسائل الدفع",
                  "لوحات تحكم إدارية"
                ],
                link: "/services/web-development",
                color: "bg-blue-500"
              },
              {
                title: "تطوير تطبيقات الموبايل",
                description: "تطبيقات ذكية ومبتكرة لجميع المنصات تعزز تفاعل عملائك مع علامتك التجارية",
                icon: "📱",
                features: [
                  "تطبيقات iOS و Android أصلية",
                  "تطبيقات متقاطعة المنصات",
                  "تطبيقات الويب التقدمية PWA",
                  "تكامل مع الخدمات السحابية",
                  "إشعارات فورية",
                  "تحليلات الاستخدام"
                ],
                link: "/services/mobile-development",
                color: "bg-green-500"
              },
              {
                title: "الاستضافة وإدارة السيرفرات",
                description: "حلول استضافة موثوقة وآمنة مع إدارة احترافية للسيرفرات والبنية التحتية",
                icon: "☁️",
                features: [
                  "استضافة سحابية عالية الأداء",
                  "إدارة السيرفرات والصيانة",
                  "نسخ احتياطية تلقائية",
                  "مراقبة الأداء 24/7",
                  "شهادات SSL مجانية",
                  "دعم فني متخصص"
                ],
                link: "/services/hosting",
                color: "bg-purple-500"
              },
              {
                title: "الأتمتة والتكاملات",
                description: "أتمتة العمليات التجارية وربط الأنظمة المختلفة لتحسين الكفاءة والإنتاجية",
                icon: "⚙️",
                features: [
                  "أتمتة سير العمل",
                  "تكامل الأنظمة والخدمات",
                  "APIs مخصصة",
                  "معالجة البيانات التلقائية",
                  "تقارير وتحليلات ذكية",
                  "إشعارات وتنبيهات تلقائية"
                ],
                link: "/services/automation",
                color: "bg-orange-500"
              },
              {
                title: "برنامج نمو SaaS",
                description: "استراتيجيات وحلول متخصصة لنمو منتجات البرمجيات كخدمة وزيادة قاعدة المستخدمين",
                icon: "📈",
                features: [
                  "استراتيجيات النمو المخصصة",
                  "تحليل البيانات والمقاييس",
                  "تحسين معدلات التحويل",
                  "أتمتة التسويق",
                  "إدارة دورة حياة العميل",
                  "تطوير المنتج المدفوع بالبيانات"
                ],
                link: "/services/saas-growth",
                color: "bg-indigo-500"
              }
            ].map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-8 hover:bg-white hover:shadow-xl transition-all duration-300"
              >
                <div className={`${service.color} w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl mb-6`}>
                  {service.icon}
                </div>
                <h3 className="text-2xl font-bold text-primary mb-4">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-700 mb-3">ما نقدمه:</h4>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className="w-2 h-2 bg-accent rounded-full ml-3 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <Link
                  href={service.link}
                  className="inline-block bg-accent hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300"
                >
                  تفاصيل أكثر
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              كيف نعمل معك؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              منهجية مدروسة ومجربة لضمان نجاح مشروعك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "الاستشارة والتحليل",
                description: "نفهم احتياجاتك ونحلل متطلبات مشروعك بدقة",
                icon: "💡"
              },
              {
                step: "02", 
                title: "التخطيط والتصميم",
                description: "نضع خطة مفصلة ونصمم الحل الأمثل لك",
                icon: "📋"
              },
              {
                step: "03",
                title: "التطوير والتنفيذ",
                description: "ننفذ المشروع باستخدام أحدث التقنيات والممارسات",
                icon: "⚙️"
              },
              {
                step: "04",
                title: "التسليم والدعم",
                description: "نسلم المشروع مع التدريب والدعم المستمر",
                icon: "🚀"
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg"
              >
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">
                  {process.step}
                </div>
                <div className="text-4xl mb-4">{process.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لبدء مشروعك التقني؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احصل على استشارة مجانية واكتشف كيف يمكن لخدماتنا أن تساعد في نمو أعمالك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احصل على استشارة مجانية
              </Link>
              <Link
                href="/portfolio"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                شاهد أعمالنا السابقة
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default ServicesPage
