'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const RecommendedPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'جميع المنتجات', icon: '🌟' },
    { id: 'hosting', name: 'الاستضافة والسحابة', icon: '☁️' },
    { id: 'security', name: 'الأمان والحماية', icon: '🛡️' },
    { id: 'payments', name: 'المدفوعات', icon: '💳' },
    { id: 'marketing', name: 'التسويق', icon: '📧' },
    { id: 'productivity', name: 'الإنتاجية', icon: '📊' },
    { id: 'design', name: 'التصميم', icon: '🎨' }
  ]

  const products = [
    {
      id: 1,
      name: "Cloudflare",
      category: "security",
      description: "خدمة CDN وحماية مواقع الويب الرائدة عالمياً مع أداء فائق وحماية من الهجمات",
      features: [
        "شبكة توصيل المحتوى العالمية",
        "حماية من هجمات DDoS",
        "تحسين سرعة الموقع",
        "شهادات SSL مجانية",
        "تحليلات مفصلة",
        "حماية من البوتات"
      ],
      pricing: "مجاني - $200/شهر",
      rating: 4.8,
      icon: "🛡️",
      link: "https://cloudflare.com",
      affiliate: true,
      badge: "الأكثر شيوعاً"
    },
    {
      id: 2,
      name: "DigitalOcean",
      category: "hosting",
      description: "منصة استضافة سحابية موثوقة ومرنة مصممة خصيصاً للمطورين والشركات الناشئة",
      features: [
        "خوادم SSD عالية الأداء",
        "شبكة عالمية",
        "واجهة سهلة الاستخدام",
        "نسخ احتياطية تلقائية",
        "مراقبة الأداء",
        "دعم فني ممتاز"
      ],
      pricing: "$5 - $960/شهر",
      rating: 4.7,
      icon: "☁️",
      link: "https://digitalocean.com",
      affiliate: true
    },
    {
      id: 3,
      name: "Stripe",
      category: "payments",
      description: "منصة دفع آمنة وسهلة التكامل تدعم جميع طرق الدفع الحديثة مع أدوات متقدمة للتجارة",
      features: [
        "دعم جميع طرق الدفع",
        "أمان عالي المستوى",
        "تكامل سهل مع APIs",
        "لوحة تحكم شاملة",
        "تقارير مالية مفصلة",
        "دعم العملات المتعددة"
      ],
      pricing: "2.9% + 30¢ لكل معاملة",
      rating: 4.6,
      icon: "💳",
      link: "https://stripe.com",
      affiliate: true,
      badge: "موصى به"
    },
    {
      id: 4,
      name: "Mailchimp",
      category: "marketing",
      description: "أداة تسويق بريد إلكتروني احترافية مع أتمتة متقدمة وتحليلات شاملة",
      features: [
        "حملات بريد إلكتروني احترافية",
        "أتمتة التسويق",
        "تحليلات مفصلة",
        "قوالب جاهزة",
        "تكامل مع منصات أخرى",
        "اختبار A/B"
      ],
      pricing: "مجاني - $299/شهر",
      rating: 4.5,
      icon: "📧",
      link: "https://mailchimp.com",
      affiliate: true
    },
    {
      id: 5,
      name: "Google Workspace",
      category: "productivity",
      description: "مجموعة أدوات العمل والتعاون من جوجل تشمل Gmail وDrive وDocs وMeet",
      features: [
        "بريد إلكتروني احترافي",
        "تخزين سحابي آمن",
        "أدوات التعاون",
        "اجتماعات فيديو",
        "تطبيقات الإنتاجية",
        "أمان متقدم"
      ],
      pricing: "$6 - $18/مستخدم/شهر",
      rating: 4.4,
      icon: "📊",
      link: "https://workspace.google.com",
      affiliate: false
    },
    {
      id: 6,
      name: "Figma",
      category: "design",
      description: "أداة التصميم التعاوني الرائدة للواجهات والنماذج الأولية مع إمكانيات التعاون الفوري",
      features: [
        "تصميم تعاوني فوري",
        "نماذج أولية تفاعلية",
        "مكتبة مكونات",
        "تصدير للمطورين",
        "تعليقات وملاحظات",
        "تكامل مع أدوات أخرى"
      ],
      pricing: "مجاني - $15/محرر/شهر",
      rating: 4.7,
      icon: "🎨",
      link: "https://figma.com",
      affiliate: false,
      badge: "اختيار المصممين"
    },
    {
      id: 7,
      name: "AWS",
      category: "hosting",
      description: "منصة الحوسبة السحابية الأكثر شمولية وموثوقية من أمازون",
      features: [
        "خدمات سحابية شاملة",
        "موثوقية عالية",
        "أمان متقدم",
        "مرونة في التوسع",
        "شبكة عالمية",
        "دعم فني متخصص"
      ],
      pricing: "حسب الاستخدام",
      rating: 4.5,
      icon: "☁️",
      link: "https://aws.amazon.com",
      affiliate: true
    },
    {
      id: 8,
      name: "Notion",
      category: "productivity",
      description: "مساحة عمل شاملة تجمع الملاحظات والمهام وقواعد البيانات والتعاون في مكان واحد",
      features: [
        "ملاحظات ومستندات",
        "قواعد بيانات مرنة",
        "إدارة المشاريع",
        "تعاون الفريق",
        "قوالب جاهزة",
        "تكاملات متعددة"
      ],
      pricing: "مجاني - $10/مستخدم/شهر",
      rating: 4.6,
      icon: "📝",
      link: "https://notion.so",
      affiliate: true
    }
  ]

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.category === selectedCategory)

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">⭐</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              منتجات نوصي بها
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              مجموعة مختارة بعناية من أفضل الأدوات والخدمات التقنية التي نستخدمها ونوصي بها لعملائنا. 
              كل منتج تم اختباره واستخدامه في مشاريع حقيقية.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="#products"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تصفح المنتجات
              </Link>
              <Link 
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                استشارة مجانية
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Why We Recommend */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لماذا نوصي بهذه المنتجات؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              معايير صارمة في الاختيار لضمان أفضل النتائج لمشاريعك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "✅",
                title: "مجربة ومختبرة",
                description: "استخدمناها في مشاريع حقيقية وحققت نتائج ممتازة"
              },
              {
                icon: "🏆",
                title: "جودة عالية",
                description: "منتجات رائدة في مجالها مع تقييمات عالية من المستخدمين"
              },
              {
                icon: "🔧",
                title: "سهولة التكامل",
                description: "تتكامل بسهولة مع الأنظمة الأخرى وسهلة الاستخدام"
              },
              {
                icon: "💰",
                title: "قيمة مقابل السعر",
                description: "توفر قيمة ممتازة مقابل التكلفة مع خطط مرنة"
              }
            ].map((reason, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{reason.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{reason.title}</h3>
                <p className="text-gray-600 text-sm">{reason.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section id="products" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              المنتجات الموصى بها
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              اختر من مجموعة منتقاة من أفضل الأدوات التقنية
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-accent text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-xl ml-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift border border-gray-100 relative"
              >
                {product.badge && (
                  <div className="absolute top-4 left-4 bg-accent text-white text-xs font-semibold px-3 py-1 rounded-full z-10">
                    {product.badge}
                  </div>
                )}

                <div className="bg-gradient-to-br from-primary to-primary-600 h-32 flex items-center justify-center relative">
                  <div className="text-6xl">{product.icon}</div>
                  {product.affiliate && (
                    <div className="absolute bottom-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                      رابط شراكة
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-xl font-bold text-primary">{product.name}</h3>
                    <div className="flex items-center">
                      <span className="text-yellow-400 text-sm">⭐</span>
                      <span className="text-gray-600 text-sm ml-1">{product.rating}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-4 text-sm line-clamp-3">{product.description}</p>

                  <div className="mb-4">
                    <div className="text-accent font-semibold mb-2">{product.pricing}</div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">المميزات الرئيسية:</h4>
                    <ul className="space-y-1">
                      {product.features.slice(0, 4).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-xs text-gray-600">
                          <div className="w-1.5 h-1.5 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex gap-2">
                    <a
                      href={product.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-accent hover:bg-accent-600 text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-colors duration-300"
                    >
                      زيارة الموقع
                    </a>
                    <Link
                      href="/contact"
                      className="flex-1 border-2 border-accent text-accent hover:bg-accent hover:text-white py-2 px-4 rounded-lg font-semibold text-sm text-center transition-all duration-300"
                    >
                      استشارة
                    </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Integration Services */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خدمات التكامل والإعداد
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نساعدك في تكامل وإعداد هذه الأدوات مع أنظمتك الحالية
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "إعداد وتكوين",
                description: "نقوم بإعداد وتكوين الأدوات حسب احتياجاتك",
                icon: "⚙️",
                price: "من 500 ريال"
              },
              {
                title: "التكامل مع الأنظمة",
                description: "ربط الأدوات مع أنظمتك الحالية عبر APIs",
                icon: "🔗",
                price: "من 1,000 ريال"
              },
              {
                title: "التدريب والدعم",
                description: "تدريب فريقك على استخدام الأدوات بكفاءة",
                icon: "🎓",
                price: "من 300 ريال"
              },
              {
                title: "الصيانة والتحديث",
                description: "صيانة دورية وتحديثات للحفاظ على الأداء",
                icon: "🔧",
                price: "من 200 ريال/شهر"
              },
              {
                title: "استشارة التحسين",
                description: "تحليل وتحسين استخدامك للأدوات",
                icon: "📈",
                price: "من 800 ريال"
              },
              {
                title: "الهجرة والنقل",
                description: "نقل بياناتك من أدوات أخرى بأمان",
                icon: "📦",
                price: "من 1,500 ريال"
              }
            ].map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{service.title}</h3>
                <p className="text-gray-600 text-sm mb-4">{service.description}</p>
                <div className="text-accent font-semibold mb-4">{service.price}</div>
                <Link
                  href="/contact"
                  className="block w-full text-center bg-gray-100 hover:bg-accent hover:text-white py-2 rounded-lg font-semibold text-sm transition-colors duration-300"
                >
                  اطلب الخدمة
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              تحتاج مساعدة في الاختيار؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احصل على استشارة مجانية لاختيار الأدوات المناسبة لمشروعك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                استشارة مجانية
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث معنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default RecommendedPage
