'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const AffiliatePage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    website: '',
    experience: '',
    audience: '',
    marketing_channels: '',
    message: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
  }

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🤝</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              كن شريكنا في النجاح
              <span className="block text-accent">برنامج الشراكة بالعمولة</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              انضم إلى برنامج الشراكة الأكثر ربحية في مجال التقنية. احصل على عمولات مجزية مقابل كل عميل تحيله إلينا 
              واستفد من دعمنا الشامل ومواردنا التسويقية المتقدمة.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="#apply"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                انضم الآن
              </Link>
              <Link 
                href="#benefits"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف المزايا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Commission Structure */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              هيكل العمولات المجزية
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              عمولات تنافسية تزيد مع نجاحك وإنجازاتك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                level: "شريك مبتدئ",
                commission: "15%",
                requirements: "0-5 إحالات شهرياً",
                features: [
                  "عمولة 15% على جميع المبيعات",
                  "مواد تسويقية أساسية",
                  "دعم عبر البريد الإلكتروني",
                  "تقارير شهرية",
                  "دفعات شهرية"
                ],
                color: "bg-blue-500"
              },
              {
                level: "شريك متقدم",
                commission: "20%",
                requirements: "6-15 إحالة شهرياً",
                features: [
                  "عمولة 20% على جميع المبيعات",
                  "مواد تسويقية متقدمة",
                  "دعم هاتفي مخصص",
                  "تقارير أسبوعية",
                  "دفعات نصف شهرية",
                  "بونص أداء إضافي"
                ],
                color: "bg-green-500",
                popular: true
              },
              {
                level: "شريك VIP",
                commission: "25%",
                requirements: "16+ إحالة شهرياً",
                features: [
                  "عمولة 25% على جميع المبيعات",
                  "مواد تسويقية حصرية",
                  "مدير حساب مخصص",
                  "تقارير يومية",
                  "دفعات أسبوعية",
                  "بونص أداء مضاعف",
                  "دعوات لفعاليات خاصة"
                ],
                color: "bg-purple-500"
              }
            ].map((tier, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 relative ${
                  tier.popular ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {tier.popular && (
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full">
                    الأكثر شيوعاً
                  </div>
                )}
                
                <div className={`${tier.color} w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6`}>
                  {tier.commission}
                </div>
                
                <h3 className="text-xl font-bold text-primary text-center mb-2">{tier.level}</h3>
                <p className="text-gray-600 text-center mb-6 text-sm">{tier.requirements}</p>
                
                <ul className="space-y-3 mb-8">
                  {tier.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start text-sm">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مزايا الشراكة معنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نقدم أكثر من مجرد عمولات - نقدم شراكة حقيقية للنجاح
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "💰",
                title: "عمولات عالية ومضمونة",
                description: "عمولات تصل إلى 25% مع ضمان الدفع في المواعيد المحددة"
              },
              {
                icon: "📈",
                title: "منتجات عالية الجودة",
                description: "منتجات وخدمات مطلوبة بقوة في السوق مع معدل تحويل عالي"
              },
              {
                icon: "🎯",
                title: "مواد تسويقية احترافية",
                description: "بانرات، فيديوهات، ومحتوى تسويقي جاهز للاستخدام"
              },
              {
                icon: "📊",
                title: "تتبع دقيق للإحالات",
                description: "نظام تتبع متقدم يضمن حصولك على عمولة كل إحالة"
              },
              {
                icon: "🎓",
                title: "تدريب وتطوير مستمر",
                description: "ورش عمل ودورات تدريبية لتطوير مهاراتك التسويقية"
              },
              {
                icon: "🤝",
                title: "دعم مخصص",
                description: "فريق دعم متخصص لمساعدتك في تحقيق أهدافك"
              },
              {
                icon: "🏆",
                title: "برامج حوافز إضافية",
                description: "مسابقات وجوائز شهرية للشركاء المتميزين"
              },
              {
                icon: "🔗",
                title: "روابط مخصصة",
                description: "روابط إحالة مخصصة لعلامتك التجارية"
              },
              {
                icon: "📱",
                title: "تطبيق شركاء",
                description: "تطبيق موبايل لمتابعة أداءك وعمولاتك"
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{benefit.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{benefit.title}</h3>
                <p className="text-gray-600 text-sm">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              كيف يعمل البرنامج؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              خطوات بسيطة للبدء في كسب العمولات
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "التسجيل",
                description: "املأ نموذج التقديم وانتظر الموافقة",
                icon: "📝"
              },
              {
                step: "02",
                title: "الحصول على الروابط",
                description: "احصل على روابط الإحالة المخصصة لك",
                icon: "🔗"
              },
              {
                step: "03",
                title: "التسويق والترويج",
                description: "ابدأ في الترويج لخدماتنا باستخدام موادنا التسويقية",
                icon: "📢"
              },
              {
                step: "04",
                title: "كسب العمولات",
                description: "احصل على عمولتك عن كل عميل يشتري من خلالك",
                icon: "💰"
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg"
              >
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">
                  {step.step}
                </div>
                <div className="text-4xl mb-4">{step.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Application Form */}
      <section id="apply" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              تقدم للانضمام الآن
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              املأ النموذج أدناه وسنتواصل معك خلال 24 ساعة
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto">
            <motion.form
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              onSubmit={handleSubmit}
              className="bg-gray-50 rounded-2xl p-8"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 font-semibold mb-2">الاسم الكامل *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                    placeholder="أدخل اسمك الكامل"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-semibold mb-2">البريد الإلكتروني *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-semibold mb-2">رقم الهاتف *</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                    placeholder="+966 50 123 4567"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-semibold mb-2">الموقع الإلكتروني</label>
                  <input
                    type="url"
                    name="website"
                    value={formData.website}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                    placeholder="https://yourwebsite.com"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-semibold mb-2">خبرتك في التسويق *</label>
                  <select
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                  >
                    <option value="">اختر مستوى خبرتك</option>
                    <option value="beginner">مبتدئ (أقل من سنة)</option>
                    <option value="intermediate">متوسط (1-3 سنوات)</option>
                    <option value="advanced">متقدم (3-5 سنوات)</option>
                    <option value="expert">خبير (أكثر من 5 سنوات)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 font-semibold mb-2">حجم جمهورك المستهدف</label>
                  <input
                    type="text"
                    name="audience"
                    value={formData.audience}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                    placeholder="مثال: 10,000 متابع على وسائل التواصل"
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-gray-700 font-semibold mb-2">قنوات التسويق التي تستخدمها</label>
                <input
                  type="text"
                  name="marketing_channels"
                  value={formData.marketing_channels}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                  placeholder="مثال: وسائل التواصل الاجتماعي، البريد الإلكتروني، المدونة"
                />
              </div>

              <div className="mt-6">
                <label className="block text-gray-700 font-semibold mb-2">رسالة إضافية</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                  placeholder="أخبرنا عن خططك التسويقية وكيف تخطط للترويج لخدماتنا"
                ></textarea>
              </div>

              <div className="mt-8 text-center">
                <button
                  type="submit"
                  className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
                >
                  تقدم للانضمام
                </button>
              </div>
            </motion.form>
          </div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              أسئلة شائعة
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {[
              {
                question: "كم تبلغ العمولة؟",
                answer: "تتراوح العمولة من 15% إلى 25% حسب مستوى أدائك وعدد الإحالات الشهرية."
              },
              {
                question: "متى أحصل على عمولتي؟",
                answer: "يتم دفع العمولات شهرياً للشركاء المبتدئين، ونصف شهرياً للمتقدمين، وأسبوعياً لشركاء VIP."
              },
              {
                question: "هل هناك حد أدنى للدفع؟",
                answer: "الحد الأدنى للدفع هو 100 ريال سعودي. إذا لم تصل لهذا المبلغ، سيتم ترحيله للشهر التالي."
              },
              {
                question: "كيف أتتبع إحالاتي؟",
                answer: "ستحصل على لوحة تحكم خاصة بك لمتابعة جميع إحالاتك وعمولاتك في الوقت الفعلي."
              },
              {
                question: "هل يمكنني الترويج على وسائل التواصل؟",
                answer: "نعم، يمكنك الترويج على جميع منصات التواصل الاجتماعي مع الالتزام بسياساتنا التسويقية."
              },
              {
                question: "هل هناك رسوم للانضمام؟",
                answer: "لا، الانضمام لبرنامج الشراكة مجاني تماماً ولا توجد أي رسوم مخفية."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6"
              >
                <h3 className="text-lg font-bold text-primary mb-3">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لبدء رحلة الربح؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              انضم إلى مئات الشركاء الناجحين واحصل على دخل إضافي مستمر
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="#apply"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                انضم الآن
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث معنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default AffiliatePage
