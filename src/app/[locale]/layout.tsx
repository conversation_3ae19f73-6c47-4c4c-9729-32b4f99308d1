import type { Metada<PERSON> } from 'next'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { <PERSON><PERSON><PERSON> } from 'next/font/google'
import { notFound } from 'next/navigation'
import { ReactNode } from 'react'

import { locales, Locale, languages, defaultLocale } from '@/i18n/config'
import { LocaleProvider } from '@/contexts/LocaleContext'
import StructuredData from '@/components/seo/StructuredData'
import './globals.css'

const tajawal = Tajawal({
  subsets: ['arabic', 'latin'],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-tajawal',
})



export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: rawLocale } = await params

  // Ensure we have a valid locale, fallback to default if invalid
  const locale = rawLocale && locales.includes(rawLocale as Locale) ? rawLocale : defaultLocale

  const messages = await getMessages({ locale })
  const t = (key: string) => {
    const keys = key.split('.')
    let value: any = messages
    for (const k of keys) {
      value = value?.[k]
    }
    return value || key
  }

  const isArabic = locale === 'ar'

  const baseUrl = 'https://syriana.software'
  const currentUrl = `${baseUrl}/${locale}`

  return {
    title: t('meta.defaultTitle'),
    description: t('meta.defaultDescription'),
    keywords: t('meta.keywords'),
    authors: [{ name: 'Syriana Software' }],
    creator: 'Syriana Software',
    publisher: 'SouqProcare LTD',
    robots: 'index, follow',
    metadataBase: new URL(baseUrl),
    alternates: {
      canonical: currentUrl,
      languages: {
        'ar': `${baseUrl}/ar`,
        'en': `${baseUrl}/en`,
        'x-default': `${baseUrl}/ar`
      }
    },
    openGraph: {
      type: 'website',
      locale: isArabic ? 'ar_SA' : 'en_US',
      url: currentUrl,
      title: t('meta.defaultTitle'),
      description: t('meta.defaultDescription'),
      siteName: 'Syriana Software',
      images: [
        {
          url: '/images/og-image.jpg',
          width: 1200,
          height: 630,
          alt: t('meta.defaultTitle'),
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('meta.defaultTitle'),
      description: t('meta.defaultDescription'),
      images: ['/images/og-image.jpg'],
    },
    other: {
      'google-site-verification': process.env.GOOGLE_SITE_VERIFICATION || '',
    }
  }
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale: rawLocale } = await params

  // Ensure we have a valid locale, fallback to default if invalid
  const locale = rawLocale && locales.includes(rawLocale as Locale) ? rawLocale as Locale : defaultLocale

  // Only call notFound if we can't handle the locale at all
  if (rawLocale && !locales.includes(rawLocale as Locale)) {
    notFound()
  }

  // Providing all messages to the client side is the easiest way to get started
  const messages = await getMessages({ locale })

  // Get language configuration
  const language = languages.find(lang => lang.code === locale)
  const direction = language?.dir || 'rtl'

  return (
    <html lang={locale} dir={direction}>
      <body className={`${tajawal.variable} font-tajawal antialiased`}>
        <StructuredData type="website" locale={locale} />
        <NextIntlClientProvider messages={messages}>
          <LocaleProvider initialLocale={locale as Locale}>
            {children}
          </LocaleProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
