'use client'

import { motion } from 'framer-motion'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const TermsPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">📋</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              شروط الاستخدام
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              تحكم هذه الشروط والأحكام استخدامك لموقعنا الإلكتروني وخدماتنا. 
              يرجى قراءتها بعناية قبل استخدام خدماتنا.
            </p>
            <div className="text-gray-300 text-sm">
              آخر تحديث: 19 يوليو 2025
            </div>
          </motion.div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            
            {/* Acceptance */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">قبول الشروط</h2>
              <div className="prose prose-lg text-gray-600 space-y-4">
                <p>
                  من خلال الوصول إلى موقعنا الإلكتروني أو استخدام خدماتنا، فإنك توافق على الالتزام بهذه الشروط والأحكام. 
                  إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام خدماتنا.
                </p>
                <p>
                  هذه الشروط تشكل اتفاقية قانونية ملزمة بينك وبين Syriana Software.
                </p>
              </div>
            </motion.div>

            {/* Definitions */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">التعريفات</h2>
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-800 mb-2">"الشركة" أو "نحن"</h3>
                  <p className="text-gray-600 text-sm">تشير إلى Syriana Software</p>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-800 mb-2">"المستخدم" أو "أنت"</h3>
                  <p className="text-gray-600 text-sm">تشير إلى الشخص أو الكيان الذي يستخدم خدماتنا</p>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-800 mb-2">"الخدمات"</h3>
                  <p className="text-gray-600 text-sm">تشير إلى جميع المنتجات والخدمات التي نقدمها</p>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="font-semibold text-gray-800 mb-2">"الموقع"</h3>
                  <p className="text-gray-600 text-sm">تشير إلى موقعنا الإلكتروني وجميع صفحاته</p>
                </div>
              </div>
            </motion.div>

            {/* Use of Services */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">استخدام الخدمات</h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">الاستخدام المسموح</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      استخدام الخدمات للأغراض التجارية المشروعة
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      الوصول إلى المحتوى والمعلومات المتاحة
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      التواصل معنا للحصول على الدعم
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      مشاركة المحتوى مع الإشارة للمصدر
                    </li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-3">الاستخدام المحظور</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      استخدام الخدمات لأي أغراض غير قانونية
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      محاولة اختراق أو إلحاق الضرر بالأنظمة
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      نسخ أو توزيع المحتوى دون إذن
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      إرسال محتوى مسيء أو ضار
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      انتحال الشخصية أو تقديم معلومات كاذبة
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Intellectual Property */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">الملكية الفكرية</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  جميع المحتويات والمواد الموجودة على موقعنا، بما في ذلك النصوص والصور والشعارات والبرامج، 
                  محمية بموجب قوانين حقوق الطبع والنشر والملكية الفكرية.
                </p>
                <div className="bg-yellow-50 border-r-4 border-yellow-400 p-4">
                  <h3 className="font-semibold text-yellow-800 mb-2">⚠️ تنبيه مهم</h3>
                  <p className="text-yellow-700 text-sm">
                    لا يجوز نسخ أو توزيع أو تعديل أي من محتوياتنا دون الحصول على إذن كتابي مسبق منا.
                  </p>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold text-gray-800">حقوقنا تشمل:</h3>
                  <ul className="space-y-1">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span>العلامات التجارية والشعارات</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span>أكواد البرمجة والتطبيقات</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span>التصاميم والواجهات</span>
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span>المحتوى النصي والمرئي</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Payment Terms */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">شروط الدفع</h2>
              <div className="space-y-4 text-gray-600">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-semibold text-gray-800 mb-3">💳 طرق الدفع</h3>
                    <ul className="space-y-1 text-sm">
                      <li>• التحويل البنكي</li>
                      <li>• البطاقات الائتمانية</li>
                      <li>• المحافظ الإلكترونية</li>
                      <li>• الدفع عند التسليم (حسب الخدمة)</li>
                    </ul>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="font-semibold text-gray-800 mb-3">📅 مواعيد الدفع</h3>
                    <ul className="space-y-1 text-sm">
                      <li>• دفعة مقدمة 50% عند البدء</li>
                      <li>• الباقي عند التسليم</li>
                      <li>• الاشتراكات تُدفع مقدماً</li>
                      <li>• تأخير الدفع يوقف الخدمة</li>
                    </ul>
                  </div>
                </div>
                <div className="bg-blue-50 border-r-4 border-blue-400 p-4">
                  <h3 className="font-semibold text-blue-800 mb-2">ℹ️ ملاحظة</h3>
                  <p className="text-blue-700 text-sm">
                    جميع الأسعار المذكورة بالريال السعودي وتشمل ضريبة القيمة المضافة حيثما ينطبق.
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Refund Policy */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">سياسة الاسترداد</h2>
              <div className="space-y-4 text-gray-600">
                <div className="space-y-4">
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="font-semibold text-green-800 mb-2">✅ حالات الاسترداد المقبولة</h3>
                    <ul className="space-y-1 text-sm text-green-700">
                      <li>• عدم تسليم الخدمة في الموعد المحدد</li>
                      <li>• عدم مطابقة الخدمة للمواصفات المتفق عليها</li>
                      <li>• مشاكل تقنية لا يمكن حلها</li>
                      <li>• إلغاء الطلب خلال 24 ساعة من التأكيد</li>
                    </ul>
                  </div>
                  <div className="bg-red-50 rounded-lg p-4">
                    <h3 className="font-semibold text-red-800 mb-2">❌ حالات عدم الاسترداد</h3>
                    <ul className="space-y-1 text-sm text-red-700">
                      <li>• الخدمات المخصصة بعد البدء في التنفيذ</li>
                      <li>• تغيير رأي العميل بعد التسليم</li>
                      <li>• الخدمات المستهلكة (مثل الاستشارات)</li>
                      <li>• انتهاك العميل لشروط الاستخدام</li>
                    </ul>
                  </div>
                </div>
                <p className="text-sm">
                  <strong>مدة الاسترداد:</strong> يجب تقديم طلب الاسترداد خلال 30 يوماً من تاريخ الشراء.
                </p>
              </div>
            </motion.div>

            {/* Liability */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">المسؤولية والضمانات</h2>
              <div className="space-y-4 text-gray-600">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="font-semibold text-gray-800 mb-3">ضماناتنا</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      نضمن جودة الخدمات المقدمة حسب المواصفات المتفق عليها
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      نضمن سرية وأمان بياناتك
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      نضمن الدعم الفني حسب الاتفاق
                    </li>
                  </ul>
                </div>
                <div className="bg-yellow-50 rounded-lg p-6">
                  <h3 className="font-semibold text-yellow-800 mb-3">حدود المسؤولية</h3>
                  <ul className="space-y-2 text-sm text-yellow-700">
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      لا نتحمل مسؤولية الأضرار غير المباشرة
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      مسؤوليتنا محدودة بقيمة الخدمة المدفوعة
                    </li>
                    <li className="flex items-start">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      لا نضمن عدم انقطاع الخدمة بنسبة 100%
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Termination */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">إنهاء الخدمة</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  يحق لأي من الطرفين إنهاء الاتفاقية في الحالات التالية:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3">إنهاء من جانبنا</h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                        انتهاك شروط الاستخدام
                      </li>
                      <li className="flex items-start">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                        عدم دفع المستحقات
                      </li>
                      <li className="flex items-start">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                        استخدام غير قانوني للخدمة
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-3">إنهاء من جانبك</h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-start">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                        إشعار مسبق 30 يوماً
                      </li>
                      <li className="flex items-start">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                        تسوية جميع المستحقات
                      </li>
                      <li className="flex items-start">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                        إرجاع المواد المملوكة لنا
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Governing Law */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">القانون الحاكم</h2>
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="space-y-4 text-gray-600">
                  <p>
                    تخضع هذه الشروط والأحكام للقوانين المصرية، وأي نزاع ينشأ عنها يخضع لاختصاص المحاكم المصرية.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-semibold text-gray-800 mb-2">حل النزاعات</h3>
                      <ul className="space-y-1 text-sm">
                        <li>1. التفاوض المباشر</li>
                        <li>2. الوساطة</li>
                        <li>3. التحكيم</li>
                        <li>4. المحاكم المختصة</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800 mb-2">الاختصاص القضائي</h3>
                      <p className="text-sm">محاكم القاهرة، جمهورية مصر العربية</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-12"
            >
              <h2 className="text-2xl font-bold text-primary mb-6">تواصل معنا</h2>
              <div className="bg-gray-50 rounded-lg p-6">
                <p className="text-gray-600 mb-4">
                  لأي استفسارات حول هذه الشروط والأحكام، يرجى التواصل معنا:
                </p>
                <div className="space-y-2 text-gray-600">
                  <div className="flex items-center">
                    <span className="font-semibold ml-2">البريد الإلكتروني:</span>
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-semibold ml-2">الهاتف:</span>
                    <span>+20 106 617 0179</span>
                  </div>
                  <div className="flex items-center">
                    <span className="font-semibold ml-2">العنوان:</span>
                    <span>مصر، القاهرة</span>
                  </div>
                </div>
              </div>
            </motion.div>

          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default TermsPage
