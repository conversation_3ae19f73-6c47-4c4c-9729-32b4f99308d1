'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const AboutPage = () => {
  const t = useTranslations('about')
  const locale = useLocale()
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🏢</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('hero.title')}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.subtitle')}
            </p>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
                {t('story.title')}
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  {t('story.paragraph1')}
                </p>
                <p>
                  {t('story.paragraph2')}
                </p>
                <p>
                  {t('story.paragraph3')}
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <h3 className="text-2xl font-bold text-primary mb-6">{t('stats.title')}</h3>
              <div className="grid grid-cols-2 gap-6">
                {[
                  { number: "100+", label: t('stats.projects') },
                  { number: "50+", label: t('stats.clients') },
                  { number: "5+", label: t('stats.experience') },
                  { number: "15+", label: t('stats.experts') },
                  { number: "24/7", label: t('stats.support') },
                  { number: "99%", label: t('stats.satisfaction') }
                ].map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl md:text-3xl font-bold text-accent mb-2">{stat.number}</div>
                    <div className="text-gray-600 text-sm">{stat.label}</div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('mission.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('mission.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-gradient-to-br from-primary to-primary-600 text-white rounded-2xl p-8"
            >
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-2xl font-bold mb-4">{t('mission.vision.title')}</h3>
              <p className="text-white/90 leading-relaxed">
                {t('mission.vision.description')}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-gradient-to-br from-accent to-accent-600 text-white rounded-2xl p-8"
            >
              <div className="text-4xl mb-4">🚀</div>
              <h3 className="text-2xl font-bold mb-4">{t('mission.mission.title')}</h3>
              <p className="text-white/90 leading-relaxed">
                {t('mission.mission.description')}
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('values.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('values.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {(locale === 'ar' ? [
              {
                icon: "⭐",
                title: "الجودة",
                description: "نلتزم بأعلى معايير الجودة في كل ما نقدمه"
              },
              {
                icon: "🤝",
                title: "الشراكة",
                description: "نبني علاقات طويلة الأمد مع عملائنا"
              },
              {
                icon: "💡",
                title: "الابتكار",
                description: "نسعى دائماً لتقديم حلول مبتكرة ومتطورة"
              },
              {
                icon: "🎯",
                title: "التركيز على النتائج",
                description: "نركز على تحقيق أهداف عملائنا ونجاحهم"
              },
              {
                icon: "🔒",
                title: "الأمانة",
                description: "نتعامل بشفافية وصدق في جميع تعاملاتنا"
              },
              {
                icon: "⚡",
                title: "السرعة",
                description: "نقدر وقت عملائنا ونلتزم بالمواعيد المحددة"
              },
              {
                icon: "🌟",
                title: "التميز",
                description: "نسعى للتميز في كل تفصيلة صغيرة"
              },
              {
                icon: "📚",
                title: "التعلم المستمر",
                description: "نواكب أحدث التقنيات والاتجاهات"
              }
            ] : [
              {
                icon: "⭐",
                title: "Quality",
                description: "We commit to the highest quality standards in everything we deliver"
              },
              {
                icon: "🤝",
                title: "Partnership",
                description: "We build long-term relationships with our clients"
              },
              {
                icon: "💡",
                title: "Innovation",
                description: "We always strive to provide innovative and advanced solutions"
              },
              {
                icon: "🎯",
                title: "Results-Focused",
                description: "We focus on achieving our clients' goals and success"
              },
              {
                icon: "🔒",
                title: "Integrity",
                description: "We deal with transparency and honesty in all our interactions"
              },
              {
                icon: "⚡",
                title: "Speed",
                description: "We value our clients' time and commit to deadlines"
              },
              {
                icon: "🌟",
                title: "Excellence",
                description: "We strive for excellence in every small detail"
              },
              {
                icon: "📚",
                title: "Continuous Learning",
                description: "We keep up with the latest technologies and trends"
              }
            ]).map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{value.title}</h3>
                <p className="text-gray-600 text-sm">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              {t('team.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              {t('team.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {(locale === 'ar' ? [
              {
                name: "فريق التطوير",
                role: "مطورون متخصصون",
                description: "خبراء في أحدث تقنيات البرمجة والتطوير",
                icon: "👨‍💻"
              },
              {
                name: "فريق التصميم",
                role: "مصممو واجهات",
                description: "متخصصون في تصميم تجربة المستخدم",
                icon: "🎨"
              },
              {
                name: "فريق إدارة المشاريع",
                role: "مديرو مشاريع",
                description: "ضمان تسليم المشاريع في الوقت المحدد",
                icon: "📋"
              },
              {
                name: "فريق الدعم الفني",
                role: "خبراء الدعم",
                description: "دعم فني متاح على مدار الساعة",
                icon: "🛠️"
              }
            ] : [
              {
                name: "Development Team",
                role: "Specialized Developers",
                description: "Experts in the latest programming and development technologies",
                icon: "👨‍💻"
              },
              {
                name: "Design Team",
                role: "UI/UX Designers",
                description: "Specialists in user experience design",
                icon: "🎨"
              },
              {
                name: "Project Management Team",
                role: "Project Managers",
                description: "Ensuring projects are delivered on time",
                icon: "📋"
              },
              {
                name: "Technical Support Team",
                role: "Support Experts",
                description: "24/7 technical support available",
                icon: "🛠️"
              }
            ]).map((team, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-6 text-center hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-5xl mb-4">{team.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-2">{team.name}</h3>
                <p className="text-accent font-semibold mb-3">{team.role}</p>
                <p className="text-gray-600 text-sm">{team.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Legal Information Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              معلومات قانونية
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              شركة مرخصة ومسجلة رسمياً في عدة دول
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <h3 className="text-2xl font-bold text-primary mb-6">الكيانات القانونية</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="text-2xl ml-4 text-accent">🇬🇧</div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-1">المملكة المتحدة</h4>
                    <p className="text-gray-600">SouqProcare LTD</p>
                    <p className="text-sm text-gray-500">شركة مسجلة في إنجلترا وويلز</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="text-2xl ml-4 text-accent">🇪🇬</div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-1">جمهورية مصر العربية</h4>
                    <p className="text-gray-600">Souq Pro Care.Com لتكنولوجيا المعلومات</p>
                    <p className="text-sm text-gray-500">شركة مسجلة في وزارة التجارة والصناعة</p>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <h3 className="text-2xl font-bold text-primary mb-6">العلامات التجارية</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="text-2xl ml-4 text-accent">®</div>
                  <div>
                    <h4 className="font-semibold text-gray-800">SouqProcare</h4>
                    <p className="text-sm text-gray-600">علامة تجارية مسجلة</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="text-2xl ml-4 text-accent">®</div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Syriana Software</h4>
                    <p className="text-sm text-gray-600">سوريانا سوفت وير - علامة تجارية مسجلة</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  جميع الحقوق محفوظة © 2025 Syriana Software
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد للانضمام إلى عائلة عملائنا؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              دعنا نكون شريكك في رحلة النجاح الرقمي
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                ابدأ مشروعك معنا
              </Link>
              <Link
                href="/portfolio"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                شاهد أعمالنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default AboutPage
