'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const FAQPage = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const t = useTranslations('faq')
  const locale = useLocale()

  const categories = locale === 'ar' ? [
    { id: 'all', name: 'جميع الأسئلة', icon: '❓' },
    { id: 'services', name: 'الخدمات', icon: '🔧' },
    { id: 'pricing', name: 'الأسعار', icon: '💰' },
    { id: 'technical', name: 'تقني', icon: '⚙️' },
    { id: 'support', name: 'الدعم', icon: '🎧' },
    { id: 'partnership', name: 'الشراكة', icon: '🤝' }
  ] : [
    { id: 'all', name: 'All Questions', icon: '❓' },
    { id: 'services', name: 'Services', icon: '🔧' },
    { id: 'pricing', name: 'Pricing', icon: '💰' },
    { id: 'technical', name: 'Technical', icon: '⚙️' },
    { id: 'support', name: 'Support', icon: '🎧' },
    { id: 'partnership', name: 'Partnership', icon: '🤝' }
  ]

  const faqs = locale === 'ar' ? [
    {
      id: 1,
      category: 'services',
      question: 'ما هي الخدمات التي تقدمونها؟',
      answer: 'نقدم مجموعة شاملة من الخدمات التقنية تشمل تطوير المواقع والتطبيقات، الاستضافة وإدارة السيرفرات، الأتمتة والتكاملات، وبرنامج نمو SaaS المتخصص. كما نقدم برامج جاهزة مثل dash و sybooking و sylink و ease.'
    },
    {
      id: 2,
      category: 'pricing',
      question: 'كيف يتم تحديد أسعار الخدمات؟',
      answer: 'تعتمد أسعارنا على عدة عوامل: تعقيد المشروع، الوقت المطلوب للتنفيذ، التقنيات المستخدمة، ومستوى التخصيص المطلوب. نقدم عروض أسعار مخصصة لكل مشروع بعد دراسة المتطلبات بالتفصيل.'
    },
    {
      id: 3,
      category: 'technical',
      question: 'ما هي التقنيات التي تستخدمونها؟',
      answer: 'نستخدم أحدث التقنيات مثل React, Next.js, Node.js, Python, PHP, MySQL, MongoDB, AWS, Docker, وغيرها. نختار التقنية الأنسب لكل مشروع حسب المتطلبات والأهداف.'
    },
    {
      id: 4,
      category: 'services',
      question: 'كم يستغرق تطوير موقع إلكتروني؟',
      answer: 'يعتمد الوقت على تعقيد الموقع. موقع بسيط يستغرق 1-2 أسبوع، موقع متوسط 2-4 أسابيع، وموقع معقد أو متجر إلكتروني قد يستغرق 4-8 أسابيع. نقدم جدولاً زمنياً مفصلاً لكل مشروع.'
    },
    {
      id: 5,
      category: 'support',
      question: 'ما نوع الدعم الفني الذي تقدمونه؟',
      answer: 'نقدم دعماً فنياً شاملاً يشمل: دعم عبر البريد الإلكتروني والهاتف، صيانة دورية، تحديثات أمنية، نسخ احتياطية، ومراقبة الأداء. كما نقدم تدريباً لفريقك على استخدام الأنظمة.'
    },
    {
      id: 6,
      category: 'pricing',
      question: 'هل تقدمون خطط دفع مرنة؟',
      answer: 'نعم، نقدم خطط دفع مرنة تشمل: دفعة مقدمة 50% عند البدء والباقي عند التسليم، أو تقسيط على دفعات شهرية للمشاريع الكبيرة. كما نقدم خصومات للعملاء الدائمين.'
    }
  ] : [
    {
      id: 1,
      category: 'services',
      question: 'What services do you provide?',
      answer: 'We provide a comprehensive range of technical services including website and application development, hosting and server management, automation and integrations, and specialized SaaS growth programs. We also offer ready-made programs like dash, sybooking, sylink, and ease.'
    },
    {
      id: 2,
      category: 'pricing',
      question: 'How are service prices determined?',
      answer: 'Our prices depend on several factors: project complexity, required execution time, technologies used, and level of customization needed. We provide custom quotes for each project after studying the requirements in detail.'
    },
    {
      id: 3,
      category: 'technical',
      question: 'What technologies do you use?',
      answer: 'We use the latest technologies such as React, Next.js, Node.js, Python, PHP, MySQL, MongoDB, AWS, Docker, and others. We choose the most suitable technology for each project based on requirements and objectives.'
    },
    {
      id: 4,
      category: 'services',
      question: 'How long does it take to develop a website?',
      answer: 'The time depends on the complexity of the website. A simple website takes 1-2 weeks, a medium website 2-4 weeks, and a complex website or e-commerce store may take 4-8 weeks. We provide a detailed timeline for each project.'
    },
    {
      id: 5,
      category: 'support',
      question: 'What type of technical support do you provide?',
      answer: 'We provide comprehensive technical support including: email and phone support, regular maintenance, security updates, backups, and performance monitoring. We also provide training for your team on using the systems.'
    },
    {
      id: 6,
      category: 'pricing',
      question: 'Do you offer flexible payment plans?',
      answer: 'Yes, we offer flexible payment plans including: 50% down payment at start and the rest upon delivery, or monthly installments for large projects. We also offer discounts for returning customers.'
    }
  ]

  const filteredFAQs = selectedCategory === 'all'
    ? faqs
    : faqs.filter(faq => faq.category === selectedCategory)

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id)
  }

  return (
    <main className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">❓</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('hero.title')}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              {t('hero.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="#faqs"
                className="bg-accent hover:bg-accent/90 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover-lift"
              >
                تصفح الأسئلة
              </Link>
              <Link
                href="/contact"
                className="bg-white/10 hover:bg-white/20 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover-lift backdrop-blur-sm"
              >
                اسأل سؤالاً جديداً
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4 mb-16">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 hover-lift ${
                  selectedCategory === category.id
                    ? 'bg-primary text-white shadow-lg'
                    : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                <span className="mr-2">{category.icon}</span>
                {category.name}
              </button>
            ))}
          </div>

          {/* FAQ List */}
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.map((faq, index) => (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="mb-4"
              >
                <div className="bg-white rounded-lg shadow-md overflow-hidden">
                  <button
                    onClick={() => toggleFAQ(faq.id)}
                    className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                  >
                    <span className="font-semibold text-gray-800 pr-4">
                      {faq.question}
                    </span>
                    <div className={`transform transition-transform duration-200 ${
                      openFAQ === faq.id ? 'rotate-180' : ''
                    }`}>
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>

                  <motion.div
                    initial={false}
                    animate={{
                      height: openFAQ === faq.id ? 'auto' : 0,
                      opacity: openFAQ === faq.id ? 1 : 0
                    }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-4 text-gray-600 leading-relaxed">
                      {faq.answer}
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default FAQPage
