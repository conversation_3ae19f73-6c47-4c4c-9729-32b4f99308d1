'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useState } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const BlogPage = () => {
  const t = useTranslations('blog')
  const locale = useLocale()
  const [selectedCategory, setSelectedCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'جميع المقالات', icon: '📚' },
    { id: 'web-dev', name: 'تطوير الويب', icon: '🌐' },
    { id: 'mobile-dev', name: 'تطبيقات الموبايل', icon: '📱' },
    { id: 'ai', name: 'الذكاء الاصطناعي', icon: '🤖' },
    { id: 'business', name: 'ريادة الأعمال', icon: '💼' },
    { id: 'tutorials', name: 'دروس تعليمية', icon: '🎓' }
  ]

  const articles = [
    {
      id: 1,
      title: "دليل شامل لتطوير المواقع الإلكترونية في 2025",
      category: "web-dev",
      excerpt: "تعرف على أحدث التقنيات والأدوات المستخدمة في تطوير المواقع الإلكترونية، من React و Next.js إلى أفضل الممارسات في التصميم والأمان.",
      content: "محتوى المقال الكامل هنا...",
      author: "فريق Syriana Software",
      publishDate: "2025-01-15",
      readTime: "8 دقائق",
      tags: ["React", "Next.js", "تطوير الويب", "JavaScript"],
      image: "🌐",
      featured: true
    },
    {
      id: 2,
      title: "كيفية بناء تطبيق موبايل ناجح: من الفكرة إلى النشر",
      category: "mobile-dev",
      excerpt: "خطوات عملية لتحويل فكرتك إلى تطبيق موبايل ناجح، بما في ذلك التخطيط والتصميم والتطوير والتسويق.",
      content: "محتوى المقال الكامل هنا...",
      author: "أحمد محمد",
      publishDate: "2025-01-12",
      readTime: "12 دقيقة",
      tags: ["React Native", "Flutter", "تطبيقات الموبايل", "UX/UI"],
      image: "📱",
      featured: false
    },
    {
      id: 3,
      title: "الذكاء الاصطناعي في خدمة العملاء: ثورة حقيقية",
      category: "ai",
      excerpt: "كيف يمكن للذكاء الاصطناعي أن يحول خدمة العملاء ويحسن تجربة المستخدم، مع أمثلة عملية وحالات استخدام.",
      content: "محتوى المقال الكامل هنا...",
      author: "سارة أحمد",
      publishDate: "2025-01-10",
      readTime: "10 دقائق",
      tags: ["الذكاء الاصطناعي", "خدمة العملاء", "ChatBot", "تعلم الآلة"],
      image: "🤖",
      featured: true
    },
    {
      id: 4,
      title: "استراتيجيات نمو الشركات الناشئة في العصر الرقمي",
      category: "business",
      excerpt: "نصائح وإرشادات للشركات الناشئة حول كيفية النمو والتوسع في السوق الرقمي، مع التركيز على التقنيات الحديثة.",
      content: "محتوى المقال الكامل هنا...",
      author: "خالد السعيد",
      publishDate: "2025-01-08",
      readTime: "15 دقيقة",
      tags: ["ريادة الأعمال", "نمو الشركات", "التسويق الرقمي", "SaaS"],
      image: "💼",
      featured: false
    },
    {
      id: 5,
      title: "تعلم React من الصفر: دليل المبتدئين الشامل",
      category: "tutorials",
      excerpt: "دورة تعليمية شاملة لتعلم React من البداية، مع أمثلة عملية ومشاريع تطبيقية لترسيخ المفاهيم.",
      content: "محتوى المقال الكامل هنا...",
      author: "فاطمة العلي",
      publishDate: "2025-01-05",
      readTime: "20 دقيقة",
      tags: ["React", "JavaScript", "تعليم البرمجة", "Frontend"],
      image: "🎓",
      featured: false
    },
    {
      id: 6,
      title: "أمان المواقع الإلكترونية: دليل شامل للحماية",
      category: "web-dev",
      excerpt: "كل ما تحتاج معرفته لحماية موقعك الإلكتروني من التهديدات السيبرانية، مع أفضل الممارسات والأدوات.",
      content: "محتوى المقال الكامل هنا...",
      author: "محمد حسن",
      publishDate: "2025-01-03",
      readTime: "14 دقيقة",
      tags: ["أمان الويب", "HTTPS", "حماية البيانات", "Cybersecurity"],
      image: "🔒",
      featured: false
    },
    {
      id: 7,
      title: "مستقبل التجارة الإلكترونية: اتجاهات 2025",
      category: "business",
      excerpt: "نظرة على أحدث الاتجاهات في التجارة الإلكترونية وكيف يمكن للشركات الاستفادة منها لزيادة مبيعاتها.",
      content: "محتوى المقال الكامل هنا...",
      author: "نور الدين",
      publishDate: "2025-01-01",
      readTime: "11 دقيقة",
      tags: ["التجارة الإلكترونية", "التسويق", "تجربة المستخدم", "الذكاء الاصطناعي"],
      image: "🛒",
      featured: true
    },
    {
      id: 8,
      title: "بناء API احترافي باستخدام Node.js و Express",
      category: "tutorials",
      excerpt: "تعلم كيفية بناء API قوي وآمن باستخدام Node.js و Express، مع أفضل الممارسات في التصميم والأمان.",
      content: "محتوى المقال الكامل هنا...",
      author: "عبدالله أحمد",
      publishDate: "2024-12-28",
      readTime: "18 دقيقة",
      tags: ["Node.js", "Express", "API", "Backend"],
      image: "⚙️",
      featured: false
    }
  ]

  const filteredArticles = selectedCategory === 'all' 
    ? articles 
    : articles.filter(article => article.category === selectedCategory)

  const featuredArticles = articles.filter(article => article.featured)

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">📝</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              مدونة Syriana Software
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              مقالات تقنية متخصصة، دروس تعليمية، ونصائح عملية في عالم البرمجة وتطوير البرمجيات. 
              نشارك معك خبراتنا وأحدث الاتجاهات التقنية.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="#featured"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                المقالات المميزة
              </Link>
              <Link 
                href="#all-articles"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                جميع المقالات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Featured Articles */}
      <section id="featured" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              المقالات المميزة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              أحدث وأهم المقالات التي نوصي بقراءتها
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredArticles.map((article, index) => (
              <motion.div
                key={article.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift"
              >
                <div className="bg-gradient-to-br from-primary to-primary-600 h-32 flex items-center justify-center">
                  <div className="text-6xl">{article.image}</div>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm font-semibold">
                      مميز
                    </span>
                    <span className="text-gray-500 text-sm">{article.readTime}</span>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-3 line-clamp-2">{article.title}</h3>
                  <p className="text-gray-600 mb-4 text-sm line-clamp-3">{article.excerpt}</p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>{article.author}</span>
                    <span>{new Date(article.publishDate).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {article.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <Link
                    href={`/blog/${article.id}`}
                    className="block w-full text-center bg-accent hover:bg-accent-600 text-white py-3 rounded-lg font-semibold transition-colors duration-300"
                  >
                    اقرأ المقال
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* All Articles */}
      <section id="all-articles" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              جميع المقالات
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              تصفح مقالاتنا حسب الفئة التي تهمك
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-accent text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-xl ml-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>
          </motion.div>

          {/* Articles Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredArticles.map((article, index) => (
              <motion.div
                key={article.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift border border-gray-100"
              >
                <div className="bg-gray-100 h-24 flex items-center justify-center">
                  <div className="text-4xl">{article.image}</div>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-gray-500 text-sm">{article.readTime}</span>
                    {article.featured && (
                      <span className="bg-accent/10 text-accent px-2 py-1 rounded text-xs font-semibold">
                        مميز
                      </span>
                    )}
                  </div>
                  <h3 className="text-lg font-bold text-primary mb-3 line-clamp-2">{article.title}</h3>
                  <p className="text-gray-600 mb-4 text-sm line-clamp-3">{article.excerpt}</p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>{article.author}</span>
                    <span>{new Date(article.publishDate).toLocaleDateString('ar-SA')}</span>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {article.tags.slice(0, 2).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <Link
                    href={`/blog/${article.id}`}
                    className="block w-full text-center bg-gray-100 hover:bg-accent hover:text-white py-2 rounded-lg font-semibold text-sm transition-colors duration-300"
                  >
                    اقرأ المقال
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Subscription */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto text-center"
          >
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="text-5xl mb-6">📧</div>
              <h2 className="text-3xl font-bold text-primary mb-4">
                اشترك في نشرتنا الإخبارية
              </h2>
              <p className="text-gray-600 mb-6">
                احصل على أحدث المقالات والنصائح التقنية مباشرة في بريدك الإلكتروني
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="أدخل بريدك الإلكتروني"
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent focus:border-transparent"
                />
                <button className="bg-accent hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300">
                  اشترك
                </button>
              </div>
              <p className="text-gray-500 text-sm mt-4">
                لن نرسل لك رسائل مزعجة. يمكنك إلغاء الاشتراك في أي وقت.
              </p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              هل لديك فكرة لمقال؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              نرحب بمساهماتك ومقالاتك التقنية. شاركنا خبرتك مع المجتمع التقني العربي
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                اقترح مقالاً
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تواصل معنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default BlogPage
