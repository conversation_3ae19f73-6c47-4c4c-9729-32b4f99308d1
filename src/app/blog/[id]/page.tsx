'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const BlogArticlePage = () => {
  const params = useParams()
  const articleId = params.id

  // في التطبيق الحقيقي، ستجلب البيانات من API أو قاعدة بيانات
  const article = {
    id: 1,
    title: "دليل شامل لتطوير المواقع الإلكترونية في 2025",
    category: "تطوير الويب",
    excerpt: "تعرف على أحدث التقنيات والأدوات المستخدمة في تطوير المواقع الإلكترونية، من React و Next.js إلى أفضل الممارسات في التصميم والأمان.",
    author: "فريق Syriana Software",
    publishDate: "2025-01-15",
    readTime: "8 دقائق",
    tags: ["React", "Next.js", "تطوير الويب", "JavaScript"],
    image: "🌐",
    content: `
# مقدمة

في عالم التقنية المتسارع، يشهد تطوير المواقع الإلكترونية تطوراً مستمراً وظهور تقنيات جديدة بشكل دوري. مع دخولنا عام 2025، هناك العديد من الاتجاهات والتقنيات الجديدة التي يجب على كل مطور ويب أن يكون على دراية بها.

## أحدث التقنيات في تطوير الويب

### 1. React 19 والميزات الجديدة

React 19 جلب معه العديد من التحسينات المهمة:

- **Server Components**: تحسين الأداء من خلال تشغيل المكونات على الخادم
- **Concurrent Features**: تحسين تجربة المستخدم من خلال التحديثات المتزامنة
- **Automatic Batching**: تجميع التحديثات تلقائياً لتحسين الأداء

### 2. Next.js 15 والتطوير الحديث

Next.js يواصل كونه الإطار الأكثر شعبية لتطوير تطبيقات React:

- **App Router**: نظام توجيه جديد ومحسن
- **Server Actions**: تنفيذ العمليات على الخادم بسهولة
- **Turbopack**: أداة بناء سريعة جداً

### 3. TypeScript والبرمجة الآمنة

TypeScript أصبح معياراً في تطوير الويب الحديث:

- **Type Safety**: تجنب الأخطاء في وقت التطوير
- **Better IDE Support**: دعم أفضل في محررات النصوص
- **Improved Developer Experience**: تجربة تطوير محسنة

## أفضل الممارسات في التصميم

### 1. التصميم المتجاوب (Responsive Design)

- استخدام CSS Grid و Flexbox
- Mobile-First Approach
- تحسين الصور للأجهزة المختلفة

### 2. تحسين الأداء (Performance Optimization)

- **Lazy Loading**: تحميل المحتوى عند الحاجة
- **Code Splitting**: تقسيم الكود لتحميل أسرع
- **Image Optimization**: تحسين الصور وضغطها

### 3. إمكانية الوصول (Accessibility)

- استخدام ARIA Labels
- دعم لوحة المفاتيح
- تباين الألوان المناسب

## الأمان في تطوير الويب

### 1. HTTPS والتشفير

- استخدام شهادات SSL/TLS
- تشفير البيانات الحساسة
- تأمين الاتصالات

### 2. حماية من الهجمات الشائعة

- **XSS Protection**: حماية من هجمات Cross-Site Scripting
- **CSRF Protection**: حماية من هجمات Cross-Site Request Forgery
- **SQL Injection Prevention**: منع هجمات حقن SQL

### 3. إدارة المصادقة والتفويض

- استخدام JWT Tokens
- OAuth 2.0 للمصادقة الخارجية
- تشفير كلمات المرور

## أدوات التطوير الحديثة

### 1. أدوات البناء (Build Tools)

- **Vite**: أداة بناء سريعة ومرنة
- **Webpack 5**: مع ميزات التحسين الجديدة
- **Rollup**: لبناء المكتبات

### 2. أدوات الاختبار (Testing Tools)

- **Jest**: لاختبار الوحدة
- **Cypress**: للاختبار الشامل
- **Testing Library**: لاختبار المكونات

### 3. أدوات التطوير (Development Tools)

- **ESLint**: لفحص جودة الكود
- **Prettier**: لتنسيق الكود
- **Husky**: لتشغيل المهام قبل الالتزام

## الاتجاهات المستقبلية

### 1. Web Assembly (WASM)

- تشغيل كود عالي الأداء في المتصفح
- دعم لغات برمجة متعددة
- تطبيقات ويب أكثر قوة

### 2. Progressive Web Apps (PWA)

- تجربة تطبيق أصلي في المتصفح
- العمل بدون اتصال إنترنت
- إشعارات فورية

### 3. الذكاء الاصطناعي في تطوير الويب

- أدوات مساعدة في كتابة الكود
- تحسين تجربة المستخدم
- تخصيص المحتوى

## خاتمة

تطوير المواقع الإلكترونية في 2025 يتطلب مواكبة التطورات السريعة والتعلم المستمر. من المهم التركيز على الأساسيات القوية مع تبني التقنيات الجديدة تدريجياً.

النجاح في هذا المجال يتطلب:
- التعلم المستمر
- التطبيق العملي
- متابعة أحدث الاتجاهات
- التركيز على تجربة المستخدم

نأمل أن يكون هذا الدليل مفيداً لك في رحلتك في تطوير الويب. لا تتردد في التواصل معنا إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة في مشروعك القادم.
    `
  }

  const relatedArticles = [
    {
      id: 2,
      title: "كيفية بناء تطبيق موبايل ناجح: من الفكرة إلى النشر",
      image: "📱",
      readTime: "12 دقيقة"
    },
    {
      id: 5,
      title: "تعلم React من الصفر: دليل المبتدئين الشامل",
      image: "🎓",
      readTime: "20 دقيقة"
    },
    {
      id: 6,
      title: "أمان المواقع الإلكترونية: دليل شامل للحماية",
      image: "🔒",
      readTime: "14 دقيقة"
    }
  ]

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Article Header */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto text-center text-white"
          >
            <div className="text-6xl mb-6">{article.image}</div>
            <div className="bg-accent/20 text-accent-200 px-4 py-2 rounded-full text-sm font-semibold inline-block mb-4">
              {article.category}
            </div>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              {article.title}
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed">
              {article.excerpt}
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-gray-200">
              <div className="flex items-center gap-2">
                <span className="text-xl">👤</span>
                <span>{article.author}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xl">📅</span>
                <span>{new Date(article.publishDate).toLocaleDateString('ar-SA')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xl">⏱️</span>
                <span>{article.readTime}</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Article Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="prose prose-lg max-w-none"
            >
              {/* Tags */}
              <div className="flex flex-wrap gap-3 mb-8 not-prose">
                {article.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-medium"
                  >
                    #{tag}
                  </span>
                ))}
              </div>

              {/* Article Content */}
              <div 
                className="article-content text-gray-700 leading-relaxed"
                style={{ 
                  fontSize: '18px',
                  lineHeight: '1.8',
                  fontFamily: 'Tajawal, sans-serif'
                }}
                dangerouslySetInnerHTML={{ 
                  __html: article.content.replace(/\n/g, '<br>').replace(/#{1,6}\s/g, '<h2 class="text-2xl font-bold text-primary mt-8 mb-4">').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>').replace(/- (.*?)(<br>|$)/g, '<li class="mb-2">$1</li>') 
                }}
              />
            </motion.div>

            {/* Share Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mt-12 pt-8 border-t border-gray-200"
            >
              <h3 className="text-xl font-bold text-primary mb-4">شارك المقال</h3>
              <div className="flex gap-4">
                <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-300">
                  فيسبوك
                </button>
                <button className="bg-blue-400 hover:bg-blue-500 text-white px-4 py-2 rounded-lg transition-colors duration-300">
                  تويتر
                </button>
                <button className="bg-blue-700 hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors duration-300">
                  لينكد إن
                </button>
                <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-300">
                  واتساب
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مقالات ذات صلة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              مقالات أخرى قد تهمك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {relatedArticles.map((relatedArticle, index) => (
              <motion.div
                key={relatedArticle.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift"
              >
                <div className="bg-gray-100 h-24 flex items-center justify-center">
                  <div className="text-4xl">{relatedArticle.image}</div>
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-primary mb-3 line-clamp-2">
                    {relatedArticle.title}
                  </h3>
                  <div className="text-gray-500 text-sm mb-4">{relatedArticle.readTime}</div>
                  <Link
                    href={`/blog/${relatedArticle.id}`}
                    className="block w-full text-center bg-accent hover:bg-accent-600 text-white py-2 rounded-lg font-semibold transition-colors duration-300"
                  >
                    اقرأ المقال
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              هل أعجبك المقال؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              تصفح المزيد من مقالاتنا التقنية أو تواصل معنا لمناقشة مشروعك القادم
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/blog"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                المزيد من المقالات
              </Link>
              <Link
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تواصل معنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default BlogArticlePage
