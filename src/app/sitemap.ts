import { MetadataRoute } from 'next'
import { locales } from '@/i18n/config'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://syriana.software'
  
  // Define all the routes that should be included in the sitemap
  const routes = [
    '',
    '/store',
    '/about',
    '/contact',
    '/portfolio',
    '/open-source',
    '/programs/dash',
    '/programs/sybooking',
    '/programs/sylink',
    '/programs/ease',
    '/programs/syriana-bot',
    '/services/web-development',
    '/services/mobile-development',
    '/services/hosting',
    '/services/automation',
    '/services/saas-growth',
    '/partners/recommended',
    '/partners/affiliate',
    '/partners/reseller'
  ]

  // Generate sitemap entries for each route in each locale
  const sitemapEntries: MetadataRoute.Sitemap = []

  routes.forEach(route => {
    locales.forEach(locale => {
      const url = `${baseUrl}/${locale}${route}`
      
      // Determine priority based on route importance
      let priority = 0.5
      if (route === '') priority = 1.0 // Home page
      else if (route === '/store' || route === '/about' || route === '/contact') priority = 0.8
      else if (route.startsWith('/programs/') || route.startsWith('/services/')) priority = 0.7
      else if (route.startsWith('/partners/')) priority = 0.6

      // Determine change frequency
      let changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never' = 'monthly'
      if (route === '') changeFrequency = 'weekly' // Home page changes more frequently
      else if (route === '/store') changeFrequency = 'weekly' // Store updates frequently
      else if (route.startsWith('/programs/') || route.startsWith('/services/')) changeFrequency = 'monthly'

      sitemapEntries.push({
        url,
        lastModified: new Date(),
        changeFrequency,
        priority,
        alternates: {
          languages: {
            ar: `${baseUrl}/ar${route}`,
            en: `${baseUrl}/en${route}`,
          }
        }
      })
    })
  })

  return sitemapEntries
}
