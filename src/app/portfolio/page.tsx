'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const PortfolioPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">🎨</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              معرض أعمالنا
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              اكتشف مجموعة من أفضل المشاريع التي أنجزناها لعملائنا. 
              من المواقع الإلكترونية إلى التطبيقات والأنظمة المتكاملة.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                ابدأ مشروعك معنا
              </Link>
              <Link 
                href="#projects"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                شاهد المشاريع
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              إنجازاتنا بالأرقام
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              أرقام تتحدث عن جودة عملنا ورضا عملائنا
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                number: "150+",
                label: "مشروع مكتمل",
                description: "مشاريع متنوعة في مختلف المجالات"
              },
              {
                number: "80+",
                label: "عميل راضٍ",
                description: "شركات وأفراد يثقون بخدماتنا"
              },
              {
                number: "5+",
                label: "سنوات خبرة",
                description: "خبرة متراكمة في التطوير والتصميم"
              },
              {
                number: "99%",
                label: "معدل رضا العملاء",
                description: "تقييمات إيجابية من عملائنا"
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg"
              >
                <div className="text-4xl font-bold text-accent mb-2">{stat.number}</div>
                <h3 className="text-lg font-semibold text-primary mb-2">{stat.label}</h3>
                <p className="text-gray-600 text-sm">{stat.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section id="projects" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مشاريع مميزة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              مجموعة مختارة من أفضل مشاريعنا
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "منصة التجارة الإلكترونية - سوق برو",
                category: "تجارة إلكترونية",
                description: "منصة تجارة إلكترونية متكاملة مع نظام إدارة المخزون والمدفوعات",
                technologies: ["React", "Node.js", "MongoDB", "Stripe"],
                features: ["متجر متكامل", "نظام دفع آمن", "إدارة المخزون", "تطبيق موبايل"],
                image: "🛒",
                color: "bg-blue-500"
              },
              {
                title: "نظام إدارة العيادات الطبية",
                category: "صحة وطب",
                description: "نظام شامل لإدارة العيادات مع حجز المواعيد والملفات الطبية",
                technologies: ["Vue.js", "Laravel", "MySQL", "PWA"],
                features: ["حجز المواعيد", "الملفات الطبية", "نظام الفواتير", "تقارير طبية"],
                image: "🏥",
                color: "bg-green-500"
              },
              {
                title: "تطبيق توصيل الطعام - فودي",
                category: "تطبيقات الموبايل",
                description: "تطبيق توصيل طعام مع تتبع الطلبات في الوقت الفعلي",
                technologies: ["React Native", "Firebase", "Google Maps", "Stripe"],
                features: ["تتبع الطلبات", "دفع متعدد", "تقييم المطاعم", "إشعارات فورية"],
                image: "🍕",
                color: "bg-orange-500"
              },
              {
                title: "منصة التعلم الإلكتروني - إديوكيت",
                category: "تعليم",
                description: "منصة تعليمية تفاعلية مع فيديوهات واختبارات وشهادات",
                technologies: ["Next.js", "Prisma", "PostgreSQL", "AWS"],
                features: ["دورات تفاعلية", "اختبارات ذكية", "شهادات معتمدة", "تتبع التقدم"],
                image: "🎓",
                color: "bg-purple-500"
              },
              {
                title: "نظام إدارة الموارد البشرية",
                category: "أنظمة إدارية",
                description: "نظام شامل لإدارة الموظفين والرواتب والحضور",
                technologies: ["Angular", "Spring Boot", "Oracle", "Docker"],
                features: ["إدارة الموظفين", "نظام الرواتب", "تتبع الحضور", "تقارير HR"],
                image: "👥",
                color: "bg-indigo-500"
              },
              {
                title: "منصة الحجوزات السياحية",
                category: "سياحة وسفر",
                description: "منصة لحجز الرحلات السياحية والفنادق مع دليل سياحي",
                technologies: ["React", "Express.js", "MongoDB", "PayPal"],
                features: ["حجز الرحلات", "دليل سياحي", "تقييم الأماكن", "خرائط تفاعلية"],
                image: "✈️",
                color: "bg-cyan-500"
              }
            ].map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden hover-lift"
              >
                <div className={`${project.color} h-32 flex items-center justify-center`}>
                  <div className="text-6xl">{project.image}</div>
                </div>
                <div className="p-6">
                  <div className="text-sm text-accent font-semibold mb-2">{project.category}</div>
                  <h3 className="text-xl font-bold text-primary mb-3">{project.title}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{project.description}</p>
                  
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">التقنيات المستخدمة:</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">المميزات الرئيسية:</h4>
                    <ul className="space-y-1">
                      {project.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-xs text-gray-600">
                          <div className="w-1.5 h-1.5 bg-accent rounded-full ml-2 flex-shrink-0"></div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link
                    href="/contact"
                    className="block w-full text-center bg-gray-100 hover:bg-accent hover:text-white py-2 rounded-lg font-semibold text-sm transition-colors duration-300"
                  >
                    مشروع مشابه
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Categories */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مجالات خبرتنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نقدم حلولاً متخصصة في مختلف المجالات
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "🌐",
                title: "تطوير المواقع",
                count: "45+ مشروع",
                description: "مواقع ومتاجر إلكترونية احترافية"
              },
              {
                icon: "📱",
                title: "تطبيقات الموبايل",
                count: "30+ تطبيق",
                description: "تطبيقات iOS و Android أصلية ومتقاطعة"
              },
              {
                icon: "🏢",
                title: "الأنظمة الإدارية",
                count: "25+ نظام",
                description: "أنظمة إدارة مخصصة للشركات"
              },
              {
                icon: "🤖",
                title: "الذكاء الاصطناعي",
                count: "15+ حل",
                description: "حلول ذكية وبوتات تفاعلية"
              },
              {
                icon: "☁️",
                title: "الحلول السحابية",
                count: "35+ مشروع",
                description: "استضافة وحلول سحابية متقدمة"
              },
              {
                icon: "🔗",
                title: "التكاملات",
                count: "50+ تكامل",
                description: "ربط الأنظمة والخدمات المختلفة"
              },
              {
                icon: "📊",
                title: "تحليل البيانات",
                count: "20+ نظام",
                description: "لوحات معلومات وتقارير ذكية"
              },
              {
                icon: "🛡️",
                title: "الأمان السيبراني",
                count: "40+ مشروع",
                description: "حماية وأمان المواقع والتطبيقات"
              }
            ].map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{category.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-2">{category.title}</h3>
                <div className="text-accent font-semibold mb-2">{category.count}</div>
                <p className="text-gray-600 text-sm">{category.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Client Testimonials */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              آراء عملائنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              ما يقوله عملاؤنا عن تجربتهم معنا
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "أحمد محمد",
                company: "مدير شركة التقنية المتقدمة",
                testimonial: "فريق Syriana Software قدم لنا حلولاً تقنية متميزة فاقت توقعاتنا. الجودة والالتزام بالمواعيد كان استثنائياً.",
                rating: 5,
                project: "نظام إدارة المخزون"
              },
              {
                name: "فاطمة العلي",
                company: "مؤسسة العيادات الطبية",
                testimonial: "النظام الذي طوروه لنا حسن من كفاءة عملنا بشكل كبير. أصبحت إدارة المواعيد والملفات الطبية أسهل بكثير.",
                rating: 5,
                project: "نظام إدارة العيادات"
              },
              {
                name: "خالد السعيد",
                company: "متجر الإلكترونيات الذكية",
                testimonial: "المتجر الإلكتروني الذي أنشأوه لنا زاد من مبيعاتنا بنسبة 200%. التصميم رائع والوظائف متقدمة جداً.",
                rating: 5,
                project: "متجر إلكتروني"
              }
            ].map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, starIndex) => (
                    <span key={starIndex} className="text-yellow-400 text-xl">⭐</span>
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">"{testimonial.testimonial}"</p>
                <div className="border-t pt-4">
                  <h4 className="font-semibold text-primary">{testimonial.name}</h4>
                  <p className="text-gray-600 text-sm">{testimonial.company}</p>
                  <p className="text-accent text-sm font-medium">{testimonial.project}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              كيف نعمل معك؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              منهجية مدروسة لضمان نجاح مشروعك
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "الاستشارة والتخطيط",
                description: "نفهم احتياجاتك ونضع خطة مفصلة للمشروع",
                icon: "💡"
              },
              {
                step: "02",
                title: "التصميم والنماذج",
                description: "نصمم واجهات المستخدم ونماذج أولية تفاعلية",
                icon: "🎨"
              },
              {
                step: "03",
                title: "التطوير والبرمجة",
                description: "نطور المشروع باستخدام أحدث التقنيات",
                icon: "⚙️"
              },
              {
                step: "04",
                title: "الاختبار والتسليم",
                description: "نختبر المشروع بدقة ونسلمه مع التدريب والدعم",
                icon: "🚀"
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg"
              >
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">
                  {process.step}
                </div>
                <div className="text-4xl mb-4">{process.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لتحويل فكرتك إلى واقع؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              انضم إلى قائمة عملائنا الراضين واحصل على حل تقني يحقق أهدافك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                ابدأ مشروعك الآن
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث معنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default PortfolioPage
