'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const SaasGrowthPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">📈</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              برنامج نمو SaaS
              <span className="block text-accent">استراتيجيات النمو المتقدمة</span>
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              برنامج متخصص لمساعدة شركات البرمجيات كخدمة (SaaS) على تحقيق نمو مستدام وزيادة قاعدة المستخدمين 
              من خلال استراتيجيات مدروسة ومجربة وتحليلات عميقة للبيانات.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                احجز استشارة مجانية
              </Link>
              <Link 
                href="#features"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اكتشف الخدمات
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Problem & Solution */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="text-4xl mb-6">🎯</div>
              <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
                التحديات التي تواجه شركات SaaS
              </h2>
              <div className="space-y-4">
                {[
                  "صعوبة اكتساب عملاء جدد بتكلفة معقولة",
                  "ارتفاع معدل إلغاء الاشتراكات (Churn Rate)",
                  "تحدي زيادة قيمة العميل مدى الحياة (LTV)",
                  "صعوبة تحويل المستخدمين المجانيين إلى مدفوعين",
                  "نقص في البيانات والتحليلات الدقيقة",
                  "عدم وضوح استراتيجية النمو المناسبة"
                ].map((challenge, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-red-500 text-xl ml-3 mt-1">❌</div>
                    <p className="text-gray-600">{challenge}</p>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="text-4xl mb-6">💡</div>
              <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
                حلولنا المتخصصة
              </h2>
              <div className="space-y-4">
                {[
                  "استراتيجيات اكتساب عملاء مُحسنة التكلفة",
                  "برامج الاحتفاظ بالعملاء وتقليل Churn",
                  "تحسين قيمة العميل مدى الحياة",
                  "استراتيجيات التحويل من Freemium إلى Premium",
                  "تحليلات متقدمة ولوحات معلومات ذكية",
                  "خطط نمو مخصصة ومدروسة"
                ].map((solution, index) => (
                  <div key={index} className="flex items-start">
                    <div className="text-green-500 text-xl ml-3 mt-1">✅</div>
                    <p className="text-gray-600">{solution}</p>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خدماتنا المتخصصة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              مجموعة شاملة من الخدمات المصممة خصيصاً لنمو شركات SaaS
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "📊",
                title: "تحليل البيانات والمقاييس",
                description: "تحليل عميق لمقاييس الأداء الرئيسية وتحديد فرص التحسين",
                features: [
                  "تحليل MRR و ARR",
                  "حساب Customer LTV",
                  "تتبع Churn Rate",
                  "تحليل CAC و Payback Period"
                ]
              },
              {
                icon: "🎯",
                title: "استراتيجيات الاكتساب",
                description: "خطط مدروسة لاكتساب عملاء جدد بكفاءة وتكلفة محسنة",
                features: [
                  "تحسين قنوات التسويق",
                  "استراتيجيات المحتوى",
                  "حملات مدفوعة محسنة",
                  "برامج الإحالة"
                ]
              },
              {
                icon: "🔄",
                title: "تحسين الاحتفاظ",
                description: "برامج شاملة لتقليل معدل إلغاء الاشتراكات وزيادة رضا العملاء",
                features: [
                  "تحليل أسباب الإلغاء",
                  "برامج Customer Success",
                  "تحسين تجربة المستخدم",
                  "استراتيجيات Re-engagement"
                ]
              },
              {
                icon: "💰",
                title: "تحسين الإيرادات",
                description: "استراتيجيات لزيادة الإيرادات من العملاء الحاليين",
                features: [
                  "استراتيجيات Upselling",
                  "برامج Cross-selling",
                  "تحسين نماذج التسعير",
                  "تحليل Price Elasticity"
                ]
              },
              {
                icon: "🚀",
                title: "تحسين التحويل",
                description: "تحسين معدلات التحويل في جميع مراحل القمع التسويقي",
                features: [
                  "تحسين صفحات الهبوط",
                  "A/B Testing متقدم",
                  "تحسين عملية التسجيل",
                  "تحسين Trial-to-Paid"
                ]
              },
              {
                icon: "📈",
                title: "التوسع والنمو",
                description: "استراتيجيات التوسع في أسواق جديدة وزيادة حصة السوق",
                features: [
                  "تحليل الأسواق الجديدة",
                  "استراتيجيات التوطين",
                  "شراكات استراتيجية",
                  "خطط التوسع الجغرافي"
                ]
              }
            ].map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-4 text-sm">{service.description}</p>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                      <div className="w-2 h-2 bg-accent rounded-full ml-3 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              منهجيتنا في العمل
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              عملية مدروسة ومجربة لضمان تحقيق أفضل النتائج
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "التحليل والتقييم",
                description: "تحليل شامل للوضع الحالي وتحديد نقاط القوة والضعف",
                icon: "🔍"
              },
              {
                step: "02",
                title: "وضع الاستراتيجية",
                description: "تطوير استراتيجية نمو مخصصة بناءً على التحليل",
                icon: "📋"
              },
              {
                step: "03",
                title: "التنفيذ والتطبيق",
                description: "تنفيذ الاستراتيجية مع متابعة دقيقة للنتائج",
                icon: "⚙️"
              },
              {
                step: "04",
                title: "التحسين المستمر",
                description: "مراقبة الأداء وتحسين الاستراتيجية باستمرار",
                icon: "📈"
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg"
              >
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">
                  {process.step}
                </div>
                <div className="text-4xl mb-4">{process.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              نتائج مثبتة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              إحصائيات حقيقية من عملائنا الذين حققوا نمواً استثنائياً
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                metric: "300%",
                description: "متوسط نمو MRR",
                icon: "💰"
              },
              {
                metric: "65%",
                description: "تقليل Churn Rate",
                icon: "📉"
              },
              {
                metric: "250%",
                description: "زيادة Customer LTV",
                icon: "👥"
              },
              {
                metric: "180%",
                description: "تحسين معدل التحويل",
                icon: "🎯"
              }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-gray-50 rounded-2xl p-8"
              >
                <div className="text-4xl mb-4">{stat.icon}</div>
                <div className="text-4xl font-bold text-accent mb-2">{stat.metric}</div>
                <p className="text-gray-600">{stat.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              خطط الخدمة
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              اختر الخطة التي تناسب حجم شركتك ومرحلة نموها
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              {
                name: "الاستشارة الأولية",
                price: "2,999",
                period: "لمرة واحدة",
                description: "تقييم شامل ووضع خطة نمو أولية",
                features: [
                  "تحليل الوضع الحالي",
                  "تحديد فرص النمو",
                  "خطة عمل أولية",
                  "جلسة استشارة 3 ساعات",
                  "تقرير مفصل"
                ],
                recommended: false
              },
              {
                name: "برنامج النمو الشامل",
                price: "12,999",
                period: "شهرياً",
                description: "برنامج متكامل لتحقيق نمو مستدام",
                features: [
                  "جميع خدمات الاستشارة الأولية",
                  "تنفيذ استراتيجيات النمو",
                  "متابعة أسبوعية",
                  "تحليلات متقدمة",
                  "تحسين مستمر",
                  "دعم فني مخصص"
                ],
                recommended: true
              },
              {
                name: "الشراكة الاستراتيجية",
                price: "حسب الاتفاق",
                period: "سنوياً",
                description: "شراكة طويلة المدى مع مشاركة في النتائج",
                features: [
                  "جميع خدمات البرنامج الشامل",
                  "فريق مخصص",
                  "مشاركة في الأرباح",
                  "تطوير مشترك للاستراتيجيات",
                  "أولوية في الدعم",
                  "اجتماعات شهرية مع الإدارة"
                ],
                recommended: false
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className={`bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 relative ${
                  plan.recommended ? 'ring-2 ring-accent transform scale-105' : ''
                }`}
              >
                {plan.recommended && (
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-accent text-white text-sm font-semibold px-4 py-2 rounded-full">
                    الأكثر شيوعاً
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-primary mb-2">{plan.name}</h3>
                  <div className="text-3xl font-bold text-accent mb-1">{plan.price} ريال</div>
                  <div className="text-gray-500 text-sm">{plan.period}</div>
                </div>
                
                <p className="text-gray-600 text-center mb-6">{plan.description}</p>
                
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start text-sm">
                      <div className="w-2 h-2 bg-accent rounded-full mt-2 ml-3 flex-shrink-0"></div>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Link
                  href="/contact"
                  className={`block w-full text-center py-3 rounded-lg font-semibold transition-colors duration-300 ${
                    plan.recommended
                      ? 'bg-accent hover:bg-accent-600 text-white'
                      : 'bg-gray-100 hover:bg-accent hover:text-white text-gray-700'
                  }`}
                >
                  ابدأ الآن
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لتسريع نمو شركتك؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احجز استشارة مجانية مع خبرائنا واكتشف كيف يمكننا مساعدتك في تحقيق نمو استثنائي
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احجز استشارة مجانية
              </Link>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تحدث مع خبير
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default SaasGrowthPage
