'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const MobileDevelopmentPage = () => {
  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">📱</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              تطوير تطبيقات الموبايل
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              نطور تطبيقات ذكية ومبتكرة لجميع المنصات تساعدك في الوصول لعملائك في أي مكان وزمان
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اطلب عرض سعر
              </Link>
              <Link 
                href="/portfolio"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                شاهد تطبيقاتنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Platforms Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              نطور لجميع المنصات
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              تطبيقات متوافقة مع جميع الأجهزة والأنظمة
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: "🍎",
                title: "iOS",
                description: "تطبيقات أصلية لأجهزة iPhone و iPad",
                features: ["Swift", "Objective-C", "SwiftUI", "App Store"]
              },
              {
                icon: "🤖",
                title: "Android",
                description: "تطبيقات متطورة لجميع أجهزة Android",
                features: ["Kotlin", "Java", "Android Studio", "Google Play"]
              },
              {
                icon: "🔄",
                title: "Cross-Platform",
                description: "تطبيق واحد يعمل على جميع المنصات",
                features: ["React Native", "Flutter", "Xamarin", "Ionic"]
              }
            ].map((platform, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
              >
                <div className="text-5xl mb-4 text-center">{platform.icon}</div>
                <h3 className="text-2xl font-bold text-primary mb-3 text-center">{platform.title}</h3>
                <p className="text-gray-600 mb-6 text-center">{platform.description}</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {platform.features.map((feature, featureIndex) => (
                    <span
                      key={featureIndex}
                      className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm font-medium"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مميزات تطبيقاتنا
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نركز على تقديم تجربة مستخدم استثنائية مع أداء عالي
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: "⚡",
                title: "أداء سريع",
                description: "تطبيقات محسنة للأداء العالي والاستجابة السريعة"
              },
              {
                icon: "🎨",
                title: "تصميم جذاب",
                description: "واجهات مستخدم عصرية وسهلة الاستخدام"
              },
              {
                icon: "🔒",
                title: "أمان متقدم",
                description: "حماية قوية للبيانات والخصوصية"
              },
              {
                icon: "🌐",
                title: "تكامل API",
                description: "ربط سلس مع الخدمات والأنظمة الخارجية"
              },
              {
                icon: "📊",
                title: "تحليلات مفصلة",
                description: "إحصائيات شاملة عن استخدام التطبيق"
              },
              {
                icon: "🔄",
                title: "تحديثات تلقائية",
                description: "نظام تحديث ذكي للمحتوى والميزات"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              مراحل تطوير التطبيق
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              من الفكرة إلى النشر في المتاجر الرسمية
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {[
              {
                step: "01",
                title: "التخطيط",
                description: "تحليل المتطلبات ووضع الاستراتيجية"
              },
              {
                step: "02",
                title: "التصميم",
                description: "تصميم واجهات المستخدم والتجربة"
              },
              {
                step: "03",
                title: "التطوير",
                description: "برمجة التطبيق وتطوير الميزات"
              },
              {
                step: "04",
                title: "الاختبار",
                description: "اختبارات شاملة على جميع الأجهزة"
              },
              {
                step: "05",
                title: "النشر",
                description: "رفع التطبيق للمتاجر الرسمية"
              }
            ].map((process, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-4">
                  {process.step}
                </div>
                <h3 className="text-lg font-bold text-primary mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Types Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              أنواع التطبيقات التي نطورها
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              نغطي جميع أنواع التطبيقات لمختلف القطاعات
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "🛒",
                title: "تطبيقات التجارة الإلكترونية",
                description: "متاجر رقمية متكاملة مع أنظمة الدفع"
              },
              {
                icon: "🏥",
                title: "تطبيقات طبية",
                description: "حلول صحية وطبية متقدمة"
              },
              {
                icon: "🎓",
                title: "تطبيقات تعليمية",
                description: "منصات تعلم تفاعلية ومبتكرة"
              },
              {
                icon: "🍕",
                title: "تطبيقات توصيل",
                description: "خدمات التوصيل والطلبات"
              },
              {
                icon: "💰",
                title: "تطبيقات مالية",
                description: "حلول مصرفية ومالية آمنة"
              },
              {
                icon: "🎮",
                title: "الألعاب",
                description: "ألعاب تفاعلية ومسلية"
              },
              {
                icon: "📱",
                title: "تطبيقات اجتماعية",
                description: "منصات التواصل والشبكات الاجتماعية"
              },
              {
                icon: "🏢",
                title: "تطبيقات الأعمال",
                description: "حلول إدارية ومؤسسية"
              }
            ].map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-gray-50 rounded-2xl p-6 text-center hover:bg-white hover:shadow-lg transition-all duration-300"
              >
                <div className="text-4xl mb-4">{type.icon}</div>
                <h3 className="text-lg font-bold text-primary mb-3">{type.title}</h3>
                <p className="text-gray-600 text-sm">{type.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              لديك فكرة تطبيق؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              دعنا نحولها إلى واقع! احصل على استشارة مجانية وتقدير تكلفة مشروعك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احصل على استشارة مجانية
              </Link>
              <Link
                href="https://wa.me/201066170179"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تواصل عبر واتساب
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default MobileDevelopmentPage
