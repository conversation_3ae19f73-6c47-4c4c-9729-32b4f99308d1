'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'

const FAQPage = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null)
  const [selectedCategory, setSelectedCategory] = useState('all')

  const categories = [
    { id: 'all', name: 'جميع الأسئلة', icon: '❓' },
    { id: 'services', name: 'الخدمات', icon: '🔧' },
    { id: 'pricing', name: 'الأسعار', icon: '💰' },
    { id: 'technical', name: 'تقني', icon: '⚙️' },
    { id: 'support', name: 'الدعم', icon: '🎧' },
    { id: 'partnership', name: 'الشراكة', icon: '🤝' }
  ]

  const faqs = [
    {
      id: 1,
      category: 'services',
      question: 'ما هي الخدمات التي تقدمونها؟',
      answer: 'نقدم مجموعة شاملة من الخدمات التقنية تشمل تطوير المواقع والتطبيقات، الاستضافة وإدارة السيرفرات، الأتمتة والتكاملات، وبرنامج نمو SaaS المتخصص. كما نقدم برامج جاهزة مثل dash و sybooking و sylink و ease.'
    },
    {
      id: 2,
      category: 'pricing',
      question: 'كيف يتم تحديد أسعار الخدمات؟',
      answer: 'تعتمد أسعارنا على عدة عوامل: تعقيد المشروع، الوقت المطلوب للتنفيذ، التقنيات المستخدمة، ومستوى التخصيص المطلوب. نقدم عروض أسعار مخصصة لكل مشروع بعد دراسة المتطلبات بالتفصيل.'
    },
    {
      id: 3,
      category: 'technical',
      question: 'ما هي التقنيات التي تستخدمونها؟',
      answer: 'نستخدم أحدث التقنيات مثل React, Next.js, Node.js, Python, PHP, MySQL, MongoDB, AWS, Docker, وغيرها. نختار التقنية الأنسب لكل مشروع حسب المتطلبات والأهداف.'
    },
    {
      id: 4,
      category: 'services',
      question: 'كم يستغرق تطوير موقع إلكتروني؟',
      answer: 'يعتمد الوقت على تعقيد الموقع. موقع بسيط يستغرق 1-2 أسبوع، موقع متوسط 2-4 أسابيع، وموقع معقد أو متجر إلكتروني قد يستغرق 4-8 أسابيع. نقدم جدولاً زمنياً مفصلاً لكل مشروع.'
    },
    {
      id: 5,
      category: 'support',
      question: 'ما نوع الدعم الفني الذي تقدمونه؟',
      answer: 'نقدم دعماً فنياً شاملاً يشمل: دعم عبر البريد الإلكتروني والهاتف، صيانة دورية، تحديثات أمنية، نسخ احتياطية، ومراقبة الأداء. كما نقدم تدريباً لفريقك على استخدام الأنظمة.'
    },
    {
      id: 6,
      category: 'pricing',
      question: 'هل تقدمون خطط دفع مرنة؟',
      answer: 'نعم، نقدم خطط دفع مرنة تشمل: دفعة مقدمة 50% عند البدء والباقي عند التسليم، أو تقسيط على دفعات شهرية للمشاريع الكبيرة. كما نقدم خصومات للعملاء الدائمين.'
    },
    {
      id: 7,
      category: 'technical',
      question: 'هل تقدمون خدمات الاستضافة؟',
      answer: 'نعم، نقدم خدمات استضافة سحابية عالية الأداء مع مراقبة 24/7، نسخ احتياطية تلقائية، شهادات SSL مجانية، ودعم فني متخصص. نستخدم خوادم موثوقة من AWS وDigitalOcean.'
    },
    {
      id: 8,
      category: 'services',
      question: 'هل يمكنكم تطوير تطبيقات الموبايل؟',
      answer: 'بالطبع! نطور تطبيقات أصلية لـ iOS و Android، وتطبيقات متقاطعة المنصات باستخدام React Native و Flutter. كما نطور تطبيقات الويب التقدمية (PWA) التي تعمل على جميع الأجهزة.'
    },
    {
      id: 9,
      category: 'partnership',
      question: 'كيف يمكنني أن أصبح شريكاً؟',
      answer: 'نقدم عدة أنواع من الشراكات: شراكة تسويق بالعمولة (15-25%)، وكالة إعادة البيع (خصومات تصل لـ40%)، والشراكة التقنية. يمكنك التقدم من خلال صفحة الشراكة أو التواصل معنا مباشرة.'
    },
    {
      id: 10,
      category: 'support',
      question: 'ما هي ساعات عمل الدعم الفني؟',
      answer: 'الدعم الفني متاح من الأحد إلى الخميس من 9 صباحاً إلى 6 مساءً (توقيت القاهرة). للحالات الطارئة، نقدم دعماً على مدار الساعة للعملاء المشتركين في خطط الدعم المتقدمة.'
    },
    {
      id: 11,
      category: 'technical',
      question: 'هل تقدمون خدمات الأمان والحماية؟',
      answer: 'نعم، نطبق أعلى معايير الأمان في جميع مشاريعنا: تشفير البيانات، حماية من الهجمات، تحديثات أمنية دورية، مراقبة الأنشطة المشبوهة، ونسخ احتياطية آمنة.'
    },
    {
      id: 12,
      category: 'services',
      question: 'هل يمكنكم تحسين موقع موجود؟',
      answer: 'بالطبع! نقدم خدمات تحسين وتطوير المواقع الموجودة: تحسين الأداء والسرعة، تحسين محركات البحث (SEO), تحديث التصميم، إضافة ميزات جديدة، وتحسين الأمان.'
    },
    {
      id: 13,
      category: 'pricing',
      question: 'هل تقدمون ضماناً على الخدمات؟',
      answer: 'نعم، نقدم ضماناً شاملاً: ضمان جودة لمدة 6 أشهر على جميع المشاريع، ضمان استرداد المال خلال 30 يوم إذا لم تكن راضياً، وضمان الدعم الفني حسب الاتفاق.'
    },
    {
      id: 14,
      category: 'technical',
      question: 'هل تقدمون خدمات التكامل مع الأنظمة الأخرى؟',
      answer: 'نعم، نتخصص في ربط الأنظمة المختلفة: تكامل مع أنظمة CRM، ERP، أنظمة الدفع، منصات التسويق، وسائل التواصل الاجتماعي، وأي APIs خارجية أخرى.'
    },
    {
      id: 15,
      category: 'support',
      question: 'هل تقدمون تدريباً على استخدام الأنظمة؟',
      answer: 'نعم، نقدم تدريباً شاملاً: جلسات تدريبية مباشرة، أدلة استخدام مفصلة، فيديوهات تعليمية، ودعم مستمر لضمان استفادتك القصوى من النظام.'
    }
  ]

  const filteredFAQs = selectedCategory === 'all' 
    ? faqs 
    : faqs.filter(faq => faq.category === selectedCategory)

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id)
  }

  return (
    <main className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 gradient-primary"></div>
        <div className="relative z-10 container mx-auto px-4 text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="text-6xl mb-6">❓</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              الأسئلة الشائعة
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed max-w-3xl mx-auto">
              إجابات شاملة على أكثر الأسئلة شيوعاً حول خدماتنا ومنتجاتنا. 
              إذا لم تجد إجابة سؤالك، لا تتردد في التواصل معنا.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="#faqs"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تصفح الأسئلة
              </Link>
              <Link 
                href="/contact"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                اسأل سؤالاً جديداً
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Quick Stats */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { number: '15+', label: 'سؤال شائع', icon: '❓' },
              { number: '6', label: 'فئة مختلفة', icon: '📂' },
              { number: '24/7', label: 'دعم متاح', icon: '🎧' },
              { number: '< 2 ساعة', label: 'وقت الاستجابة', icon: '⚡' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg"
              >
                <div className="text-4xl mb-3">{stat.icon}</div>
                <div className="text-3xl font-bold text-accent mb-2">{stat.number}</div>
                <div className="text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faqs" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              الأسئلة والإجابات
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
              اختر الفئة التي تهمك أو تصفح جميع الأسئلة
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-accent text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-xl ml-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>
          </motion.div>

          {/* FAQ List */}
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.map((faq, index) => (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="mb-4"
              >
                <div className="bg-gray-50 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
                  <button
                    onClick={() => toggleFAQ(faq.id)}
                    className="w-full px-6 py-6 text-right flex items-center justify-between hover:bg-gray-100 transition-colors duration-300"
                  >
                    <h3 className="text-lg font-semibold text-primary flex-1">
                      {faq.question}
                    </h3>
                    <div className={`text-2xl text-accent transition-transform duration-300 ${
                      openFAQ === faq.id ? 'transform rotate-45' : ''
                    }`}>
                      +
                    </div>
                  </button>
                  
                  <div className={`overflow-hidden transition-all duration-300 ${
                    openFAQ === faq.id ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                  }`}>
                    <div className="px-6 pb-6">
                      <div className="border-t border-gray-200 pt-4">
                        <p className="text-gray-600 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">
              لم تجد إجابة سؤالك؟
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              فريق الدعم الفني جاهز لمساعدتك في أي وقت
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="text-4xl mb-4">📧</div>
              <h3 className="text-lg font-bold text-primary mb-3">البريد الإلكتروني</h3>
              <p className="text-gray-600 text-sm mb-4">أرسل لنا سؤالك وسنرد خلال ساعتين</p>
              <Link
                href="mailto:<EMAIL>"
                className="text-accent hover:text-accent-600 font-semibold"
              >
                <EMAIL>
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="text-4xl mb-4">💬</div>
              <h3 className="text-lg font-bold text-primary mb-3">واتساب</h3>
              <p className="text-gray-600 text-sm mb-4">تحدث معنا مباشرة للحصول على إجابة فورية</p>
              <Link
                href="https://wa.me/201066170179"
                target="_blank"
                rel="noopener noreferrer"
                className="text-accent hover:text-accent-600 font-semibold"
              >
                +20 106 617 0179
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="text-4xl mb-4">📝</div>
              <h3 className="text-lg font-bold text-primary mb-3">نموذج التواصل</h3>
              <p className="text-gray-600 text-sm mb-4">املأ نموذج التواصل للحصول على رد مفصل</p>
              <Link
                href="/contact"
                className="text-accent hover:text-accent-600 font-semibold"
              >
                تواصل معنا
              </Link>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-3xl mx-auto"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              مستعد لبدء مشروعك؟
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              احصل على استشارة مجانية واكتشف كيف يمكننا مساعدتك
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="bg-accent hover:bg-accent-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300"
              >
                احصل على استشارة مجانية
              </Link>
              <Link
                href="/services"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
              >
                تصفح خدماتنا
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}

export default FAQPage
