#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Set environment variables
process.env.NODE_OPTIONS = '--max-old-space-size=4096';
process.env.NEXT_TELEMETRY_DISABLED = '1';

// Change to project directory
process.chdir(__dirname);

console.log('Starting Syriana Software website...');
console.log('Project directory:', __dirname);

// Start Next.js development server
const nextProcess = spawn('npx', ['next', 'dev', '--port', '3000'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
});

nextProcess.on('error', (error) => {
  console.error('Error starting Next.js:', error);
});

nextProcess.on('close', (code) => {
  console.log(`Next.js process exited with code ${code}`);
});
